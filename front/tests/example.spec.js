import { test, expect } from '@playwright/test';

test.describe('MailCode 应用测试', () => {
  test('首页应该正确加载', async ({ page }) => {
    await page.goto('/');
    
    // 检查页面标题
    await expect(page).toHaveTitle(/MailCode/);
    
    // 检查主要标题
    await expect(page.locator('h1')).toContainText('下一代');
    await expect(page.locator('h1')).toContainText('无限邮箱');
    
    // 检查导航菜单
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('text=首页')).toBeVisible();
    await expect(page.locator('text=获取邮箱')).toBeVisible();
    await expect(page.locator('text=我的邮箱')).toBeVisible();
  });

  test('导航到获取邮箱页面', async ({ page }) => {
    await page.goto('/');
    
    // 点击获取邮箱链接
    await page.click('text=获取邮箱');
    
    // 检查URL变化
    await expect(page).toHaveURL('/get-mailbox');
    
    // 检查页面内容
    await expect(page.locator('h1')).toContainText('获取邮箱');
    await expect(page.locator('text=创建您的专属无限邮箱')).toBeVisible();
  });

  test('导航到我的邮箱页面', async ({ page }) => {
    await page.goto('/');
    
    // 点击我的邮箱链接
    await page.click('text=我的邮箱');
    
    // 检查URL变化
    await expect(page).toHaveURL('/my-mailbox');
    
    // 检查页面内容
    await expect(page.locator('h1')).toContainText('我的邮箱');
  });

  test('测试登录页面', async ({ page }) => {
    await page.goto('/login');
    
    // 检查登录表单
    await expect(page.locator('h1')).toContainText('登录');
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toContainText('登录');
  });

  test('测试注册页面', async ({ page }) => {
    await page.goto('/register');
    
    // 检查注册表单
    await expect(page.locator('h1')).toContainText('注册');
    await expect(page.locator('input[placeholder*="用户名"]')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toHaveCount(2); // 密码和确认密码
    await expect(page.locator('button[type="submit"]')).toContainText('注册');
  });

  test('测试联系我们页面', async ({ page }) => {
    await page.goto('/contact');
    
    // 检查联系表单
    await expect(page.locator('h1')).toContainText('联系我们');
    await expect(page.locator('input[placeholder*="姓名"]')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[placeholder*="主题"]')).toBeVisible();
    await expect(page.locator('textarea')).toBeVisible();
  });

  test('测试响应式设计', async ({ page }) => {
    await page.goto('/');
    
    // 测试桌面视图
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.locator('nav .nav-links')).toBeVisible();
    
    // 测试移动视图
    await page.setViewportSize({ width: 375, height: 667 });
    await expect(page.locator('nav .mobile-menu-btn')).toBeVisible();
  });

  test('测试Header和Footer在页面切换时保持不变', async ({ page }) => {
    await page.goto('/');
    
    // 获取Header和Footer的内容
    const headerContent = await page.locator('nav').textContent();
    const footerContent = await page.locator('footer').textContent();
    
    // 导航到其他页面
    await page.click('text=获取邮箱');
    await page.waitForURL('/get-mailbox');
    
    // 检查Header和Footer内容是否保持不变
    await expect(page.locator('nav')).toContainText('MailCode');
    await expect(page.locator('footer')).toContainText('MailCode');

    // 再次导航
    await page.click('text=我的邮箱');
    await page.waitForURL('/my-mailbox');

    // 再次检查
    await expect(page.locator('nav')).toContainText('MailCode');
    await expect(page.locator('footer')).toContainText('MailCode');
  });
});
