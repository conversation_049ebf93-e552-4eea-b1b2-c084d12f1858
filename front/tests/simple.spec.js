import { test, expect } from '@playwright/test';

test.describe('Playwright 安装验证测试', () => {
  test('基本页面加载测试', async ({ page }) => {
    // 访问首页
    await page.goto('/');
    
    // 验证页面标题
    await expect(page).toHaveTitle(/MailCode/);
    
    // 验证页面内容
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('nav')).toBeVisible();
    await expect(page.locator('footer')).toBeVisible();
    
    console.log('✅ Playwright 安装验证成功！');
  });

  test('浏览器功能测试', async ({ page, browserName }) => {
    await page.goto('/');
    
    // 获取页面标题
    const title = await page.title();
    console.log(`🌐 浏览器: ${browserName}`);
    console.log(`📄 页面标题: ${title}`);
    
    // 测试点击功能
    await page.click('text=获取邮箱');
    await expect(page).toHaveURL('/get-mailbox');
    
    // 返回首页
    await page.click('text=首页');
    await expect(page).toHaveURL('/');
    
    console.log('✅ 浏览器交互功能正常！');
  });

  test('截图功能测试', async ({ page }) => {
    await page.goto('/');
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle');
    
    // 截图测试
    await page.screenshot({ path: 'test-results/homepage-screenshot.png' });
    
    console.log('📸 截图功能正常！');
  });
});
