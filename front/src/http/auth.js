/**
 * 认证状态管理和操作
 */
import { useUserStore } from '@/store'
import { login, register, logout } from '@/api/auth'

// 执行登录操作
export const performLogin = async (loginData) => {
  const response = await login(loginData)

  if (response.code === 200) {
    const userStore = useUserStore()
    // 登录成功后只保存token，用户信息通过getUserInfo获取
    userStore.token = response.data.token
    userStore.$persist() // 持久化token
  }
  return response
}

// 执行注册操作
export const performRegister = async (registerData) => {
  const response = await register(registerData)

  if (response.code === 200) {
    const userStore = useUserStore()
    // 注册成功后自动登录
    userStore.setUserInfo({
      id: response.data.user.id,
      username: response.data.user.username,
      email: response.data.user.email,
      mp_open_id: response.data.user.mp_open_id,
      roles: [], // 注册时还没有角色信息
      token: response.data.token
    })
  }

  return response
}

// 执行登出操作
export const performLogout = async () => {
  try {
    const userStore = useUserStore()

    // 调用后端登出接口（可选）
    if (userStore.isLoggedIn) {
      await logout()
    }
    // 清除本地用户信息
    userStore.logout()
    await MessagePlugin.success("登出成功")
  } catch (error) {
    // 即使后端接口失败，也要清除本地信息
    const userStore = useUserStore()
    userStore.logout()
    await MessagePlugin.success("登出成功")
  }
}


