# 动态路由系统说明

## 概述

本项目实现了完整的动态路由系统，参考tellyou项目的设计模式，支持基于用户权限的动态路由生成和菜单管理。

## 核心文件

### 1. 路由配置 (`/src/router/index.js`)
- `defaultRouterList`: 固定路由（登录、注册、错误页面等）
- `asyncRouterList`: 动态路由（从后端获取）
- `allRoutes`: 所有路由的合集

### 2. 权限管理 (`/src/store/modules/permission.js`)
- `generateRoutes()`: 生成动态路由
- `filterAsyncRouter()`: 转换路由格式
- `loadView()`: 动态加载组件

### 3. 路由守卫 (`/src/permission.js`)
- 前置守卫：权限验证、路由生成
- 后置守卫：登录检查、标题设置

## 路由结构

### 固定路由
```javascript
[
  '/login',      // 登录页
  '/register',   // 注册页
  '/contact',    // 联系我们
  '/home',       // 首页
  '/403',        // 权限不足
  '/404',        // 页面不存在
  '/500'         // 服务器错误
]
```

### 动态路由格式
```javascript
// 后端返回格式
{
  path: '/get-mailbox',
  name: 'GetMailbox',
  component: 'GetMailbox/index',
  meta: {
    title: '获取邮箱',
    icon: 'mail',
    affix: false
  }
}
```

## API接口

### 1. 获取用户路由
```javascript
GET /api/auth/getRouters
```

### 2. 获取用户信息
```javascript
GET /api/auth/getInfo
```

### 3. 检查登录状态
```javascript
GET /api/auth/isLogin
```

## 使用方法

### 1. 在组件中使用
```javascript
import { usePermissionStore } from '@/store'

const permissionStore = usePermissionStore()
// 路由会在路由守卫中自动生成
```

### 2. 手动生成路由
```javascript
await permissionStore.generateRoutes()
```

### 3. 检查路由权限
```javascript
const { whiteListRouters } = permissionStore
const isAllowed = whiteListRouters.includes(path)
```

## 工作流程

1. 用户访问页面
2. 路由守卫检查token
3. 如果有token但无用户信息，获取用户信息
4. 调用`generateRoutes()`生成动态路由
5. 将路由添加到Vue Router
6. 重新导航到目标页面

## 错误处理

- 支持错误重试机制（最多3次）
- 自动跳转到错误页面
- 完善的日志记录

## 白名单机制

支持通配符匹配：
```javascript
[
  '/login',
  '/register*',  // 匹配 /register 开头的所有路径
  '/public/*'    // 匹配 /public/ 下的所有路径
]
```

## 测试

访问 `/test/dynamic-route` 页面可以测试动态路由功能。
