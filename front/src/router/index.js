import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layouts/index.vue'

// 存放固定的路由（参考tellyou设计）
export const defaultRouterList = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/Register/index.vue'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/contact',
    name: 'Contact',
    component: () => import('@/views/Contact/index.vue'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Test/index.vue'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/403',
    component: () => import('@/views/403/index.vue'),
    meta: {
      hidden: true,
    },
  },
  {
    path: '/500',
    component: () => import('@/views/500/index.vue'),
    meta: {
      hidden: true,
    },
  },
  // 根路径重定向到首页（动态路由生成的是/Index）
  {
    path: '/',
    redirect: '/Index',
  },
  // 通配符路由必须放在最后
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/404/index.vue'),
    meta: {
      hidden: true,
    },
  },

]

// 存放动态路由（从后端获取）
export const asyncRouterList = []

// 所有路由（固定路由 + 动态路由）
export const allRoutes = [...defaultRouterList, ...asyncRouterList]

// 获取展开的路由（参考tellyou设计）
export const getRoutesExpanded = () => {
  const expandedRoutes = []

  allRoutes.forEach((item) => {
    if (item.meta && item.meta.expanded) {
      expandedRoutes.push(item.path)
    }
    if (item.children && item.children.length > 0) {
      item.children
        .filter((child) => child.meta && child.meta.expanded)
        .forEach((child) => {
          expandedRoutes.push(item.path)
          expandedRoutes.push(`${item.path}/${child.path}`)
        })
    }
  })
  return [...new Set(expandedRoutes)]
}

// 获取当前激活路径（参考tellyou设计）
export const getActive = (path, maxLevel = 3) => {
  if (!path) {
    return ''
  }

  return path
    .split('/')
    .filter((item, index) => index <= maxLevel && index > 0)
    .map((item) => `/${item}`)
    .join('')
}

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes: defaultRouterList,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  },
})

export default router
