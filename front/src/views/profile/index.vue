<template>
  <div class="profile-container">
    <!-- 主要内容区 -->
    <main class="main-content">
      <div class="container">
        <div class="content-wrapper">
          <!-- 主要内容 -->
          <div class="main-section">
            <!-- 个人资料概览 -->
            <section class="profile-overview">
              <div class="profile-header">
                <div class="profile-info">
                  <div class="avatar-container">
                    <div class="avatar-large">
                      {{ (userInfo.username || '用户').charAt(0).toUpperCase() }}
                    </div>
                  </div>
                  <div class="user-details">
                    <h1 class="user-name">{{ userInfo.username || '用户' }}</h1>
                    <p class="user-title">{{ userInfo.email }}</p>
                    <div class="user-badges">
                      <span class="badge verified">
                        <CheckCircle :size="16" />
                        已认证
                      </span>
                      <span class="register-date">
                        <Calendar :size="16" />
                        注册于 {{ formatDate(userInfo.create_time) }}
                      </span>
                    </div>
                  </div>
                </div>
                <div class="profile-actions">
                  <button class="btn btn-secondary">
                    <Share :size="16" />
                    分享资料
                  </button>
                  <button class="btn btn-primary" @click="handleEditProfile">
                    <Edit :size="16" />
                    编辑资料
                  </button>
                </div>
              </div>
            </section>

            <!-- 个人信息卡片 -->
            <div class="info-cards">
              <!-- 基本信息 -->
              <section class="info-card">
                <h2 class="card-title">
                  <IdCard :size="18" />
                  基本信息
                </h2>
                <div class="info-list">
                  <div class="info-item">
                    <div class="info-label">用户名</div>
                    <div class="info-value">{{ userInfo.username || '-' }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">邮箱</div>
                    <div class="info-value">{{ userInfo.email || '-' }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">微信OpenID</div>
                    <div class="info-value">{{ userInfo.mp_open_id || '未绑定' }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">创建时间</div>
                    <div class="info-value">{{ formatDateTime(userInfo.create_time) }}</div>
                  </div>
                  <div class="info-item">
                    <div class="info-label">更新时间</div>
                    <div class="info-value">{{ formatDateTime(userInfo.update_time) }}</div>
                  </div>
                </div>
              </section>

              <!-- 修改密码 -->
              <section class="info-card">
                <h2 class="card-title">
                  <Lock :size="18" />
                  修改密码
                </h2>
                <t-form
                  ref="passwordFormRef"
                  :data="passwordForm"
                  :rules="passwordRules"
                  label-width="100px"
                  @submit="onPasswordSubmit"
                >
                  <t-form-item label="当前密码" name="currentPassword">
                    <t-input
                      v-model="passwordForm.currentPassword"
                      type="password"
                      placeholder="请输入当前密码"
                      clearable
                    />
                  </t-form-item>

                  <t-form-item label="新密码" name="newPassword">
                    <t-input
                      v-model="passwordForm.newPassword"
                      type="password"
                      placeholder="请输入新密码"
                      clearable
                    />
                  </t-form-item>

                  <t-form-item label="确认新密码" name="confirmPassword">
                    <t-input
                      v-model="passwordForm.confirmPassword"
                      type="password"
                      placeholder="请再次输入新密码"
                      clearable
                    />
                  </t-form-item>

                  <t-form-item>
                    <t-button
                      theme="primary"
                      type="submit"
                      :loading="passwordLoading"
                      block
                    >
                      保存修改
                    </t-button>
                  </t-form-item>
                </t-form>
              </section>
            </div>

            <!-- 我的邮箱列表 -->
            <section class="info-card">
              <div class="mailbox-header">
                <h2 class="card-title">
                  <Mail :size="18" />
                  我的邮箱
                </h2>
                <button class="btn btn-primary btn-small" @click="handleCreateMailbox">
                  <Plus :size="16" />
                  添加更多邮箱
                </button>
              </div>
              <div class="mailbox-list">
                <!-- 加载状态 -->
                <div v-if="emailsLoading" class="loading-container">
                  <p>正在加载邮箱列表...</p>
                </div>

                <!-- 邮箱列表 -->
                <div
                  v-else
                  v-for="(mailbox, index) in userMailboxes"
                  :key="index"
                  class="mailbox-card"
                >
                  <!-- 邮箱信息 -->
                  <div class="mailbox-content">
                    <div class="mailbox-info">
                      <h3 class="mailbox-email">{{ mailbox.email }}</h3>
                      <p class="mailbox-date">创建于 {{ mailbox.createdAt }}</p>
                    </div>
                    <div class="mailbox-actions">
                      <button class="btn btn-small btn-primary" @click="handleViewMailbox(mailbox)">
                        <Eye :size="14" />
                        查看
                      </button>
                      <button class="action-btn" @click="handleCopyEmail(mailbox.email)" title="复制邮箱">
                        <Copy :size="16" />
                      </button>
                      <button class="action-btn danger" @click="handleDeleteMailbox(mailbox)" title="删除邮箱">
                        <Trash2 :size="16" />
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-if="userMailboxes.length === 0" class="empty-state">
                  <Mail :size="64" class="empty-icon" />
                  <h4 class="empty-title">暂无邮箱</h4>
                  <p class="empty-description">您还没有创建任何邮箱，点击右上角按钮添加您的第一个邮箱</p>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </main>

    <!-- 编辑资料对话框 -->
    <t-dialog
      v-model:visible="editDialogVisible"
      header="编辑个人资料"
      width="500px"
      :confirm-btn="{
        content: '保存',
        loading: editFormLoading
      }"
      @confirm="handleSaveProfile"
      @cancel="handleCancelEdit"
    >
      <div class="edit-form">
        <div class="form-group">
          <label class="form-label">用户名 <span class="required">*</span></label>
          <t-input
            v-model="editForm.username"
            placeholder="请输入用户名"
            :disabled="editFormLoading"
          />
        </div>

        <div class="form-group">
          <label class="form-label">邮箱 <span class="required">*</span></label>
          <t-input
            v-model="editForm.email"
            placeholder="请输入邮箱"
            :disabled="editFormLoading"
          />
        </div>

        <div class="form-group">
          <label class="form-label">微信OpenID</label>
          <t-input
            :value="userInfo.mp_open_id || '未绑定'"
            disabled
            placeholder="微信OpenID不可修改"
          />
          <p class="form-hint">微信OpenID不可修改</p>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { MessagePlugin } from 'tdesign-vue-next'
import { api } from '@/utils/request'
import { getUserEmailList, deleteUserEmail } from '@/api/userEmail'
import {
  IdCard,
  Lock,
  Edit,
  Share,
  CheckCircle,
  Calendar,
  Eye,
  Mail,
  Plus,
  Copy,
  Trash2
} from 'lucide-vue-next'

// 路由实例
const router = useRouter()

// 加载状态
const loading = ref(false)
const emailsLoading = ref(false)

// 编辑资料对话框状态
const editDialogVisible = ref(false)
const editFormLoading = ref(false)

// 密码表单相关状态
const passwordFormRef = ref(null)
const passwordLoading = ref(false)

// 用户信息数据
const userInfo = reactive({
  id: '',
  username: '',
  email: '',
  mp_open_id: '',
  create_time: '',
  update_time: ''
})

// 用户邮箱列表数据
const userMailboxes = ref([])

// 编辑资料表单数据
const editForm = reactive({
  username: '',
  email: ''
})

// 密码表单数据
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 密码表单校验规则
const passwordRules = reactive({
  currentPassword: [
    { required: true, message: '请输入当前密码', type: 'error' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', type: 'error' },
    { min: 8, message: '密码长度至少8位', type: 'error' },
    {
      validator: (val) => {
        // 密码必须包含字母和数字
        const hasLetter = /[a-zA-Z]/.test(val)
        const hasNumber = /\d/.test(val)
        return hasLetter && hasNumber
      },
      message: '密码必须包含字母和数字',
      type: 'error'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', type: 'error' },
    {
      validator: (val) => val === passwordForm.newPassword,
      message: '两次输入的密码不一致',
      type: 'error'
    }
  ]
})

// 获取用户信息
const fetchUserInfo = async () => {
  try {
    loading.value = true
    const response = await api.get('/api/auth/getInfo')
    console.log('📋 获取用户信息响应:', response)

    if (response.code === 200) {
      Object.assign(userInfo, response.data.user)
      console.log('👤 用户信息已更新:', userInfo)
    } else {
      console.error('获取用户信息失败:', response)
      MessagePlugin.error(response.message || '获取用户信息失败')
    }
  } catch (error) {
    console.error('❌ 获取用户信息失败:', error)
    MessagePlugin.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 获取用户邮箱列表
const fetchUserEmails = async () => {
  try {
    emailsLoading.value = true
    const response = await getUserEmailList()
    if (response.code === 200) {
      userMailboxes.value = response.data.map(item => ({
        id: item.id,
        email: item.email_address,
        createdAt: formatDateTime(item.create_time)
      }))
    }
  } catch (error) {
    console.error('获取邮箱列表失败:', error)
    MessagePlugin.error('获取邮箱列表失败')
  } finally {
    emailsLoading.value = false
  }
}

// 邮箱相关操作函数
const handleCreateMailbox = () => {
  router.push('/getMailBox')
}

const handleViewMailbox = (mailbox) => {
  router.push(`/getMailBox?email=${mailbox.email}`)
}

const handleCopyEmail = async (email) => {
  try {
    await navigator.clipboard.writeText(email)
    MessagePlugin.success(`邮箱地址已复制: ${email}`)
  } catch (err) {
    MessagePlugin.error('复制失败，请手动复制')
  }
}

const handleDeleteMailbox = async (mailbox) => {
  try {
    const confirmed = await new Promise((resolve) => {
      const result = confirm(`确定要删除邮箱 ${mailbox.email} 吗？`)
      resolve(result)
    })

    if (confirmed) {
      await deleteUserEmail(mailbox.id)
      MessagePlugin.success('邮箱删除成功')
      // 重新获取邮箱列表
      await fetchUserEmails()
    }
  } catch (error) {
    console.error('删除邮箱失败:', error)
    MessagePlugin.error('删除邮箱失败')
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 格式化日期时间
const formatDateTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 处理编辑资料
const handleEditProfile = () => {
  // 检查用户信息
  console.log('📝 准备编辑资料:', {
    userInfo: userInfo,
    userId: userInfo.id,
    username: userInfo.username,
    email: userInfo.email
  })

  // 初始化表单数据
  editForm.username = userInfo.username || ''
  editForm.email = userInfo.email || ''
  editDialogVisible.value = true
}

// 保存编辑资料
const handleSaveProfile = async () => {
  // 表单验证
  if (!editForm.username.trim()) {
    MessagePlugin.warning('请输入用户名')
    return
  }
  if (!editForm.email.trim()) {
    MessagePlugin.warning('请输入邮箱')
    return
  }

  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(editForm.email)) {
    MessagePlugin.warning('请输入正确的邮箱格式')
    return
  }

  try {
    editFormLoading.value = true

    // 添加调试信息
    console.log('🔄 更新用户信息:', {
      userId: userInfo.id,
      username: editForm.username,
      email: editForm.email
    })

    // 检查用户ID是否存在
    if (!userInfo.id) {
      throw new Error('用户ID不存在，请重新登录')
    }

    // 调用更新用户信息API
    const response = await api.put(`/api/users/${userInfo.id}`, {
      username: editForm.username,
      email: editForm.email,
      mp_open_id: userInfo.mp_open_id || null  // 确保传递 null 而不是 undefined
    })

    console.log('✅ 更新成功:', response)
    MessagePlugin.success('个人信息更新成功')
    editDialogVisible.value = false

    // 重新获取用户信息
    await fetchUserInfo()
  } catch (error) {
    console.error('❌ 更新个人信息失败:', error)
    console.error('错误详情:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    })

    // 根据不同的错误类型显示不同的消息
    if (error.response?.status === 403) {
      MessagePlugin.error('权限不足，无法修改个人信息')
    } else if (error.response?.status === 404) {
      MessagePlugin.error('用户不存在')
    } else if (error.response?.data?.message) {
      MessagePlugin.error(error.response.data.message)
    } else {
      MessagePlugin.error('更新个人信息失败，请稍后重试')
    }
  } finally {
    editFormLoading.value = false
  }
}

// 取消编辑
const handleCancelEdit = () => {
  editDialogVisible.value = false
}

// 表单提交处理
const onPasswordSubmit = async (context) => {
  console.log('表单提交:', context)

  // context.validateResult 包含校验结果
  if (context.validateResult !== true) {
    console.log('表单校验失败:', context.validateResult)
    return
  }

  try {
    passwordLoading.value = true

    await api.put('/api/auth/password', {
      oldPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    MessagePlugin.success('密码修改成功！')

    // 清空表单
    passwordForm.currentPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''

    // 重置表单校验状态
    passwordFormRef.value?.reset()
  } catch (error) {
    console.error('修改密码失败:', error)
    MessagePlugin.error(error.response?.data?.message || '修改密码失败')
  } finally {
    passwordLoading.value = false
  }
}

// 页面初始化
onMounted(async () => {
  await Promise.all([
    fetchUserInfo(),
    fetchUserEmails()
  ])
})
</script>

<style scoped lang="less">
/* 蓝色配色方案 */
@primary: #2563eb;
@primary-light: #dbeafe;
@primary-dark: #1d4ed8;
@secondary: #6b7280;
@danger: #dc2626;
@light: #f8fafc;
@dark: #1f2937;
@border: #e5e7eb;

/* 基础样式 */
.profile-container {
  font-family: 'Inter', sans-serif;
  background-color: #f9fafb;
  color: @dark;
  min-height: 100vh;
  padding: 24px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;

  @media (min-width: 640px) {
    padding: 0 24px;
  }

  @media (min-width: 1024px) {
    padding: 0 32px;
  }
}

/* 主要内容区 */
.main-content {
  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

/* 右侧主要内容 */
.main-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 个人资料概览 */
.profile-overview {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }

  .profile-header {
    display: flex;
    flex-direction: column;
    gap: 24px;

    @media (min-width: 768px) {
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }

    .profile-info {
      display: flex;
      align-items: center;
      gap: 24px;

      .avatar-container {
        .avatar-large {
          width: 96px;
          height: 96px;
          border-radius: 50%;
          background-color: @primary;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 36px;
          font-weight: 600;
          box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
          border: 4px solid white;
        }
      }

      .user-details {
        .user-name {
          font-size: 32px;
          font-weight: 700;
          color: #374151;
          margin: 0 0 4px 0;
        }

        .user-title {
          color: #6b7280;
          margin: 0 0 12px 0;
        }

        .user-badges {
          display: flex;
          align-items: center;
          gap: 16px;
          flex-wrap: wrap;

          .badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            gap: 4px;

            &.verified {
              background-color: #dbeafe;
              color: #1e40af;
            }
          }

          .register-date {
            font-size: 14px;
            color: #6b7280;
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }
    }

    .profile-actions {
      display: flex;
      gap: 12px;
      margin-top: 24px;

      @media (min-width: 768px) {
        margin-top: 0;
      }
    }
  }
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 4px;

  &.btn-primary {
    background-color: @primary;
    color: white;

    &:hover {
      background-color: @primary-dark;
    }
  }

  &.btn-secondary {
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;

    &:hover {
      background-color: #f9fafb;
    }
  }

  &.btn-full {
    width: 100%;
    justify-content: center;
    margin-top: 16px;
  }

  &.btn-small {
    padding: 6px 12px;
    font-size: 12px;
  }
}

/* 信息卡片 */
.info-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
  margin-bottom: 24px;

  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }
}

.info-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;

  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    svg {
      color: @primary;
    }
  }
}

/* 基本信息列表 */
.info-list {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .info-item {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 16px;

    .info-label {
      font-size: 14px;
      font-weight: 500;
      color: #6b7280;
    }

    .info-value {
      font-size: 14px;
      color: #374151;
      display: flex;
      align-items: center;
      gap: 8px;

      .badge.verified-small {
        display: inline-flex;
        align-items: center;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        background-color: #dcfce7;
        color: #166534;
        gap: 4px;
      }
    }
  }
}



/* 邮箱列表样式 */
.mailbox-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.mailbox-list {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .loading-container {
    text-align: center;
    padding: 40px 20px;
    color: @secondary;
  }
}

.mailbox-card {
  background: white;
  border: 1px solid @border;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s;

  &:hover {
    border-color: @primary;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
    transform: translateY(-2px);
  }

  .mailbox-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .mailbox-info {
      flex: 1;

      .mailbox-email {
        font-size: 18px;
        font-weight: 600;
        color: @dark;
        margin: 0 0 4px 0;
      }

      .mailbox-date {
        font-size: 14px;
        color: @secondary;
        margin: 0;
      }
    }

    .mailbox-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-btn {
        padding: 8px;
        border: none;
        background: none;
        border-radius: 8px;
        cursor: pointer;
        color: @secondary;
        transition: all 0.3s;

        &:hover {
          background-color: @light;
          color: @primary;
        }

        &.danger:hover {
          background-color: #fee2e2;
          color: @danger;
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 64px 24px;
  color: @secondary;

  .empty-icon {
    color: @primary;
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 24px;
    font-weight: 600;
    color: @primary;
    margin: 0 0 8px 0;
  }

  .empty-description {
    color: @secondary;
    margin: 0;
    font-size: 16px;
  }
}

/* 编辑资料对话框样式 */
.edit-form {
  .form-group {
    margin-bottom: 20px;

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: @dark;
      margin-bottom: 8px;

      .required {
        color: @danger;
        margin-left: 2px;
      }
    }

    .form-hint {
      font-size: 12px;
      color: @secondary;
      margin: 4px 0 0 0;
    }
  }
}


</style>
