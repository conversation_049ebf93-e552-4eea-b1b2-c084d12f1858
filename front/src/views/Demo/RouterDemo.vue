<template>
  <div class="router-demo">
    <t-card title="🚀 动态路由系统演示">
      <t-alert theme="info" message="这是一个演示页面，展示动态路由系统的各项功能" />
      
      <div class="demo-section">
        <h3>📊 系统状态</h3>
        <t-row :gutter="16">
          <t-col :span="6">
            <t-card size="small" :bordered="false" class="status-card">
              <div class="status-item">
                <div class="status-label">登录状态</div>
                <t-tag :theme="userStore.isLoggedIn ? 'success' : 'danger'">
                  {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
                </t-tag>
              </div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card size="small" :bordered="false" class="status-card">
              <div class="status-item">
                <div class="status-label">用户角色</div>
                <div class="status-value">{{ userStore.roles.length }} 个</div>
              </div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card size="small" :bordered="false" class="status-card">
              <div class="status-item">
                <div class="status-label">用户权限</div>
                <div class="status-value">{{ userStore.permissions.length }} 个</div>
              </div>
            </t-card>
          </t-col>
          <t-col :span="6">
            <t-card size="small" :bordered="false" class="status-card">
              <div class="status-item">
                <div class="status-label">动态路由</div>
                <div class="status-value">{{ permissionStore.menus.length }} 个</div>
              </div>
            </t-card>
          </t-col>
        </t-row>
      </div>

      <div class="demo-section">
        <h3>👤 用户信息</h3>
        <t-descriptions :data="userDescriptions" />
      </div>

      <div class="demo-section">
        <h3>🔐 权限信息</h3>
        <div class="permission-tags">
          <div class="tag-group">
            <span class="tag-label">角色:</span>
            <t-tag v-for="role in userStore.roles" :key="role.id" theme="primary" class="permission-tag">
              {{ role.name }}
            </t-tag>
          </div>
          <div class="tag-group">
            <span class="tag-label">权限:</span>
            <t-tag v-for="permission in userStore.permissions" :key="permission" theme="success" class="permission-tag">
              {{ permission }}
            </t-tag>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>🗂️ 动态路由</h3>
        <t-table :data="routeTableData" :columns="routeColumns" />
      </div>

      <div class="demo-section">
        <h3>🛠️ 功能测试</h3>
        <t-space>
          <t-button theme="primary" @click="testGenerateRoutes">
            重新生成路由
          </t-button>
          <t-button theme="success" @click="testGetUserInfo">
            刷新用户信息
          </t-button>
          <t-button theme="warning" @click="testCheckLogin">
            检查登录状态
          </t-button>
          <t-button theme="danger" @click="testClearRoutes">
            清空路由
          </t-button>
        </t-space>
      </div>
    </t-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore, usePermissionStore } from '@/store'
import { MessagePlugin } from 'tdesign-vue-next'

const route = useRoute()
const userStore = useUserStore()
const permissionStore = usePermissionStore()

// 用户信息描述
const userDescriptions = computed(() => [
  { label: '用户ID', value: userStore.userId || '未设置' },
  { label: '用户名', value: userStore.name || '未设置' },
  { label: '邮箱', value: userStore.email || '未设置' },
  { label: '显示名称', value: userStore.getDisplayName() }
])

// 路由表格数据
const routeTableData = computed(() => {
  return permissionStore.menus.map(menu => ({
    path: menu.path,
    name: menu.name,
    title: menu.meta?.title || menu.name,
    icon: menu.meta?.icon || '-',
    component: menu.component || '-'
  }))
})

// 路由表格列
const routeColumns = [
  { colKey: 'path', title: '路径', width: 200 },
  { colKey: 'name', title: '名称', width: 150 },
  { colKey: 'title', title: '标题', width: 150 },
  { colKey: 'icon', title: '图标', width: 100 },
  { colKey: 'component', title: '组件', ellipsis: true }
]

// 测试方法
const testGenerateRoutes = async () => {
  try {
    await permissionStore.generateRoutes()
    MessagePlugin.success('路由生成成功')
  } catch (error) {
    MessagePlugin.error('路由生成失败: ' + error.message)
  }
}

const testGetUserInfo = async () => {
  try {
    await userStore.getUserInfo()
    MessagePlugin.success('用户信息刷新成功')
  } catch (error) {
    MessagePlugin.error('用户信息刷新失败: ' + error.message)
  }
}

const testCheckLogin = async () => {
  try {
    const isLogin = await userStore.isLogin()
    MessagePlugin.info(`登录状态: ${isLogin ? '已登录' : '未登录'}`)
  } catch (error) {
    MessagePlugin.error('检查登录状态失败: ' + error.message)
  }
}

const testClearRoutes = () => {
  permissionStore.clearRoutes()
  MessagePlugin.warning('路由已清空')
}

onMounted(() => {
  console.log('动态路由演示页面已加载')
})
</script>

<style lang="less" scoped>
.router-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 32px;
  
  h3 {
    margin-bottom: 16px;
    color: #1f2937;
    font-size: 18px;
    font-weight: 600;
  }
}

.status-card {
  background: #f8f9fa;
  
  .status-item {
    text-align: center;
    
    .status-label {
      font-size: 12px;
      color: #6b7280;
      margin-bottom: 8px;
    }
    
    .status-value {
      font-size: 20px;
      font-weight: 600;
      color: #1f2937;
    }
  }
}

.permission-tags {
  .tag-group {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .tag-label {
      font-weight: 500;
      margin-right: 12px;
      color: #374151;
      min-width: 60px;
    }
    
    .permission-tag {
      margin-right: 8px;
      margin-bottom: 4px;
    }
  }
}
</style>
