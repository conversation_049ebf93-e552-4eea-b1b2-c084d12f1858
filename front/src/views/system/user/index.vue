<template>
  <div class="user-management">
    <!-- 蓝色横幅 - 100%宽度 -->
    <div class="header-banner">
      <div class="container">
        <div class="banner-content">
          <h1 class="page-title">系统用户管理</h1>
          <p class="page-description">管理系统用户，为用户分配角色和权限</p>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="container">
      <div class="page-content">
        <!-- 筛选表单 -->
        <div class="filter-section">
          <t-form :model="filterForm" layout="inline" @submit="handleSearch" @reset="handleReset">
            <t-form-item label="用户名" name="username">
              <t-input
                v-model="filterForm.username"
                placeholder="请输入用户名"
                clearable
                style="width: 200px"
              />
            </t-form-item>
            <t-form-item label="邮箱" name="email">
              <t-input
                v-model="filterForm.email"
                placeholder="请输入邮箱"
                clearable
                style="width: 200px"
              />
            </t-form-item>
            <t-form-item label="角色名称" name="roleName">
              <t-input
                v-model="filterForm.roleName"
                placeholder="请输入角色名称"
                clearable
                style="width: 200px"
              />
            </t-form-item>
            <t-form-item>
              <t-space>
                <t-button theme="primary" type="submit">
                  <template #icon>
                    <Search :size="16" />
                  </template>
                  搜索
                </t-button>
                <t-button variant="outline" type="reset">
                  <template #icon>
                    <RotateCcw :size="16" />
                  </template>
                  重置
                </t-button>
              </t-space>
            </t-form-item>
          </t-form>
        </div>

        <!-- 操作栏 -->
        <div class="action-bar">
          <t-button theme="primary" @click="handleAdd">
            <template #icon>
              <Plus :size="16" />
            </template>
            新增用户
          </t-button>
          <t-button variant="outline" @click="handleRefresh">
            <template #icon>
              <RefreshCw :size="16" />
            </template>
            刷新
          </t-button>
        </div>

        <!-- 用户表格 -->
        <div class="table-container">
          <t-enhanced-table
            :data="userData"
            :columns="columns"
            :loading="loading"
            row-key="id"
            :pagination="paginationConfig"
            stripe
            hover
            @page-change="handlePageChange"
          >
            <!-- 角色列 -->
            <template #roles="{ row }">
              <t-space v-if="row.roles && row.roles.length > 0" wrap>
                <t-tag
                  v-for="role in row.roles"
                  :key="role.id"
                  :theme="getRoleTagTheme(role.code)"
                  variant="light"
                  size="small"
                >
                  {{ role.name }}
                </t-tag>
              </t-space>
              <span v-else class="text-placeholder">暂无角色</span>
            </template>

            <!-- 操作列 -->
            <template #action="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                  编辑
                </t-button>
                <t-button theme="success" variant="text" size="small" @click="handleAssignRole(row)">
                  分配角色
                </t-button>
                <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-enhanced-table>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增/编辑用户对话框 -->
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    width="600px"
    :confirm-btn="{ content: '确定', loading: submitLoading }"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit="handleSubmit"
      :show-error-message="true"
    >
      <t-form-item label="用户名" name="username">
        <t-input
          v-model="formData.username"
          placeholder="请输入用户名"
        />
      </t-form-item>

      <t-form-item label="邮箱" name="email">
        <t-input
          v-model="formData.email"
          placeholder="请输入邮箱地址"
          type="email"
        />
      </t-form-item>

      <t-form-item label="密码" name="password" v-if="!editingId">
        <t-input
          v-model="formData.password"
          placeholder="请输入密码"
          type="password"
        />
      </t-form-item>

      <t-form-item label="微信OpenID" name="mp_open_id">
        <t-input
          v-model="formData.mp_open_id"
          placeholder="请输入微信小程序OpenID（可选）"
        />
      </t-form-item>
    </t-form>
  </t-dialog>

  <!-- 分配角色对话框 -->
  <t-dialog
    v-model:visible="roleDialogVisible"
    header="分配角色"
    width="600px"
    :confirm-btn="{ content: '确定', loading: roleSubmitLoading }"
    @confirm="handleRoleSubmit"
    @cancel="handleRoleCancel"
  >
    <div class="role-assignment">
      <div class="user-info">
        <h4>为用户 "{{ currentUser?.username }}" 分配角色</h4>
        <p class="text-placeholder">请选择该用户拥有的角色</p>
      </div>

      <div class="role-selection" v-if="availableRoles.length > 0">
        <t-checkbox-group v-model="selectedRoles" class="role-checkboxes">
          <div
            v-for="role in availableRoles"
            :key="role.id"
            class="role-item"
          >
            <t-checkbox
              :value="role.id"
              class="role-checkbox"
            >
              <div class="role-info-item">
                <div class="role-name">{{ role.name }}</div>
                <div class="role-code">{{ role.code }}</div>
                <div class="role-description" v-if="role.description">{{ role.description }}</div>
              </div>
            </t-checkbox>
          </div>
        </t-checkbox-group>
      </div>
      <div v-else class="loading-roles">
        <t-loading text="加载角色中..." />
      </div>
    </div>
  </t-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { Plus, RefreshCw, Search, RotateCcw } from 'lucide-vue-next'
import {
  getUserList,
  createUser,
  updateUser,
  deleteUser,
  getRoleList,
  getUserRoles,
  assignRolesToUser
} from '@/api'

// 响应式数据
const loading = ref(false)
const userData = ref([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const editingId = ref(null)

// 角色分配相关
const roleDialogVisible = ref(false)
const roleSubmitLoading = ref(false)
const currentUser = ref(null)
const availableRoles = ref([])
const selectedRoles = ref([])

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 表单数据
const formData = reactive({
  username: '',
  email: '',
  password: '',
  mp_open_id: ''
})

// 筛选表单数据
const filterForm = reactive({
  username: '',
  email: '',
  roleName: ''
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', type: 'error', trigger: ['blur', 'change'] }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', type: 'error', trigger: ['blur', 'change'] },
    { pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/, message: '请输入有效的邮箱地址', type: 'error', trigger: ['blur', 'change'] }
  ],
  password: [
    { required: true, message: '请输入密码', type: 'error', trigger: ['blur', 'change'] },
    { min: 6, message: '密码长度不能少于6位', type: 'error', trigger: ['blur', 'change'] }
  ]
}

// 表格列配置
const columns = [
  {
    colKey: 'username',
    title: '用户名',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'email',
    title: '邮箱',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'roles',
    title: '角色',
    width: 200,
    cell: 'roles'
  },
  {
    colKey: 'mp_open_id',
    title: '微信OpenID',
    width: 180,
    ellipsis: true,
    cell: (h, { row }) => row.mp_open_id || '-'
  },
  {
    colKey: 'create_time',
    title: '创建时间',
    width: 180,
    ellipsis: true
  },
  {
    colKey: 'update_time',
    title: '更新时间',
    width: 180,
    ellipsis: true
  },
  {
    colKey: 'action',
    title: '操作',
    width: 200,
    cell: 'action'
  }
]

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑用户' : '新增用户'
})

// 方法
const loadUserData = async () => {
  loading.value = true
  try {
    console.log('🔄 开始加载用户数据...')

    // 构建查询参数
    const params = {
      page: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }

    // 添加筛选条件
    if (filterForm.username) params.username = filterForm.username
    if (filterForm.email) params.email = filterForm.email
    if (filterForm.roleName) params.roleName = filterForm.roleName

    const response = await getUserList(params)
    console.log('📊 用户数据响应:', response)

    if (response.code === 200) {
      userData.value = response.data || []
      paginationConfig.total = response.total || userData.value.length
      console.log('✅ 用户数据加载成功:', {
        总数: userData.value.length,
        用户列表: userData.value.map(item => ({
          id: item.id,
          username: item.username,
          email: item.email,
          roles: item.roles
        }))
      })
    } else {
      console.error('❌ 用户数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取用户数据失败')
      userData.value = []
    }
  } catch (error) {
    console.error('❌ 加载用户数据异常:', error)
    await MessagePlugin.error('加载用户数据失败，请检查网络连接')
    userData.value = []
  } finally {
    loading.value = false
  }
}

const loadRoleData = async () => {
  try {
    console.log('🔄 开始加载角色数据...')
    const response = await getRoleList()
    console.log('📊 角色数据响应:', response)

    if (response.code === 200) {
      availableRoles.value = response.data || []
      console.log('✅ 角色数据加载成功')
    } else {
      console.error('❌ 角色数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取角色数据失败')
      availableRoles.value = []
    }
  } catch (error) {
    console.error('❌ 加载角色数据异常:', error)
    await MessagePlugin.error('加载角色数据失败，请检查网络连接')
    availableRoles.value = []
  }
}

const handlePageChange = (pageInfo) => {
  paginationConfig.current = pageInfo.current
  paginationConfig.pageSize = pageInfo.pageSize
  loadUserData()
}

const handleAdd = () => {
  resetForm()
  editingId.value = null
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  Object.assign(formData, {
    username: row.username,
    email: row.email,
    mp_open_id: row.mp_open_id || ''
  })
  editingId.value = row.id
  dialogVisible.value = true
}

const handleDelete = (row) => {
  // 显示确认对话框
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除用户"${row.username}"吗？此操作不可恢复。`,
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        console.log('🗑️ 开始删除用户:', row.id)
        const response = await deleteUser(row.id)

        if (response.code === 200) {
          await MessagePlugin.success('用户删除成功')
          await loadUserData() // 重新加载数据
          dialog.destroy() // 关闭对话框
        } else {
          await MessagePlugin.error(response.message || '删除失败')
          dialog.destroy() // 关闭对话框
        }
      } catch (error) {
        console.error('❌ 删除用户失败:', error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error('删除失败，请稍后重试')
        }
        dialog.destroy() // 关闭对话框
      }
    },
    onCancel: () => {
      console.log('🚫 用户取消删除操作')
      dialog.destroy() // 关闭对话框
    }
  })
}

const handleRefresh = () => {
  loadUserData()
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  submitLoading.value = true
  try {
    console.log('📝 提交用户数据:', formData)

    let response
    if (editingId.value) {
      // 编辑用户
      console.log('✏️ 更新用户:', editingId.value)
      response = await updateUser(editingId.value, formData)
    } else {
      // 新增用户
      console.log('➕ 创建新用户')
      response = await createUser(formData)
    }

    if (response.code === 200) {
      const action = editingId.value ? '更新' : '创建'
      await MessagePlugin.success(`用户${action}成功`)
      dialogVisible.value = false
      resetForm() // 重置表单
      await loadUserData() // 重新加载数据
    } else {
      await MessagePlugin.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('❌ 提交失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('操作失败，请稍后重试')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  // 重置表单数据
  Object.assign(formData, {
    username: '',
    email: '',
    password: '',
    mp_open_id: ''
  })
  // 使用 TDesign 表单的 reset() 方法重置表单
  formRef.value?.reset()
  // 清除验证状态
  formRef.value?.clearValidate()
}

// 角色分配相关方法
const handleAssignRole = async (row) => {
  currentUser.value = row

  // 先清空之前的选中状态
  selectedRoles.value = []

  // 显示对话框
  roleDialogVisible.value = true

  // 先加载角色数据
  await loadRoleData()

  // 等待角色数据加载完成后再加载用户角色
  await nextTick()

  // 加载用户已有的角色
  await loadUserRoles(row.id)
}

const loadUserRoles = async (userId) => {
  try {
    console.log('🔄 开始加载用户角色:', userId)
    const response = await getUserRoles(userId)
    console.log('📊 用户角色响应:', response)

    if (response.code === 200) {
      // 提取角色ID列表
      const roleIds = (response.data || []).map(role => role.id)
      console.log('📋 提取的角色ID列表:', roleIds)

      selectedRoles.value = roleIds
      console.log('✅ 用户角色加载成功，设置选中状态:', selectedRoles.value)
    } else {
      console.error('❌ 用户角色加载失败:', response.message)
      selectedRoles.value = []
    }
  } catch (error) {
    console.error('❌ 加载用户角色异常:', error)
    selectedRoles.value = []
  }
}

const handleRoleSubmit = async () => {
  if (!currentUser.value) return

  roleSubmitLoading.value = true
  try {
    console.log('📝 提交角色分配:', {
      userId: currentUser.value.id,
      roleIds: selectedRoles.value
    })

    const response = await assignRolesToUser(currentUser.value.id, selectedRoles.value)

    if (response.code === 200) {
      await MessagePlugin.success('角色分配成功')
      roleDialogVisible.value = false
    } else {
      await MessagePlugin.error(response.message || '角色分配失败')
    }
  } catch (error) {
    console.error('❌ 角色分配失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('角色分配失败，请稍后重试')
    }
  } finally {
    roleSubmitLoading.value = false
  }
}

const handleRoleCancel = () => {
  roleDialogVisible.value = false
  currentUser.value = null
  selectedRoles.value = []
}

// 筛选相关方法
const handleSearch = () => {
  paginationConfig.current = 1 // 重置到第一页
  loadUserData()
}

const handleReset = () => {
  // 重置筛选表单
  Object.assign(filterForm, {
    username: '',
    email: '',
    roleName: ''
  })
  paginationConfig.current = 1 // 重置到第一页
  loadUserData()
}

// 角色标签主题
const getRoleTagTheme = (roleCode) => {
  const themes = {
    admin: 'danger',
    manager: 'warning',
    user: 'primary',
    guest: 'default'
  }
  return themes[roleCode] || 'success'
}

// 生命周期
onMounted(() => {
  loadUserData()
})
</script>

<style lang="less" scoped>
.user-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-banner {
  width: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  padding: 40px 0;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.banner-content {
  color: white;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.page-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e7e7e7;

  :deep(.t-form) {
    .t-form-item {
      margin-bottom: 16px;
    }

    .t-form-item:last-child {
      margin-bottom: 0;
    }
  }
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e7e7e7;
}

.table-container {
  margin-top: 16px;
}

.text-placeholder {
  color: #999;
  font-style: italic;
}

// 角色分配对话框样式
.role-assignment {
  .user-info {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .text-placeholder {
      margin: 0;
      font-size: 14px;
    }
  }

  .role-selection {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    padding: 16px;
    background: #fafafa;
  }

  .role-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .role-item {
      background: white;
      border-radius: 6px;
      padding: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      transition: background-color 0.2s;

      &:hover {
        background-color: #f0f0f0;
      }

      .role-checkbox {
        width: 100%;

        :deep(.t-checkbox__label) {
          width: 100%;
        }

        .role-info-item {
          .role-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
          }

          .role-code {
            font-size: 12px;
            color: #666;
            font-family: monospace;
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            display: inline-block;
            margin-bottom: 4px;
          }

          .role-description {
            font-size: 12px;
            color: #999;
            line-height: 1.4;
          }
        }
      }
    }
  }

  .loading-roles {
    text-align: center;
    padding: 40px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-content {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .action-bar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
