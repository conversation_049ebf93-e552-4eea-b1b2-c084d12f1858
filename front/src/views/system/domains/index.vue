<template>
  <div class="domain-management">
    <!-- 蓝色横幅 - 100%宽度 -->
    <div class="header-banner">
      <div class="container">
        <div class="banner-content">
          <h1 class="page-title">域名管理</h1>
          <p class="page-description">管理系统中的域名配置，支持启用/停用域名状态</p>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="container">
      <div class="page-content">
        <!-- 筛选表单 -->
        <div class="filter-section">
          <t-form :model="filterForm" layout="inline" @submit="handleSearch" @reset="handleReset">
            <t-form-item label="域名名称" name="domain_name">
              <t-input
                v-model="filterForm.domain_name"
                placeholder="请输入域名名称"
                clearable
                style="width: 200px"
              />
            </t-form-item>
            <t-form-item label="状态" name="status">
              <t-select
                v-model="filterForm.status"
                placeholder="请选择状态"
                clearable
                style="width: 120px"
              >
                <t-option value="1" label="启用" />
                <t-option value="0" label="停用" />
              </t-select>
            </t-form-item>
            <t-form-item>
              <t-space>
                <t-button theme="primary" type="submit">
                  <template #icon>
                    <Search :size="16" />
                  </template>
                  搜索
                </t-button>
                <t-button theme="default" type="reset">
                  <template #icon>
                    <RotateCcw :size="16" />
                  </template>
                  重置
                </t-button>
              </t-space>
            </t-form-item>
          </t-form>
        </div>

        <!-- 操作栏 -->
        <div class="action-bar">
          <t-button theme="primary" @click="handleAdd">
            <template #icon>
              <Plus :size="16" />
            </template>
            新增域名
          </t-button>
          <t-button variant="outline" @click="loadDomainData">
            <template #icon>
              <RefreshCw :size="16" />
            </template>
            刷新
          </t-button>
          <t-button
            theme="danger"
            variant="outline"
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchDelete"
          >
            批量删除
          </t-button>
        </div>

        <!-- 域名表格 -->
        <div class="table-container">
          <t-enhanced-table
            :data="domainData"
            :columns="columns"
            :loading="loading"
            row-key="id"
            :pagination="paginationConfig"
            :selected-row-keys="selectedRowKeys"
            stripe
            hover
            @page-change="handlePageChange"
            @select-change="handleSelectChange"
          >
            <!-- 状态列 -->
            <template #status="{ row }">
              <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
                {{ row.status === 1 ? '启用' : '停用' }}
              </t-tag>
            </template>

            <!-- 操作列 -->
            <template #action="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                  编辑
                </t-button>
                <t-button
                  :theme="row.status === 1 ? 'warning' : 'success'"
                  variant="text"
                  size="small"
                  @click="handleToggleStatus(row)"
                >
                  {{ row.status === 1 ? '停用' : '启用' }}
                </t-button>
                <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-enhanced-table>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增/编辑域名对话框 -->
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    width="600px"
    :confirm-btn="{ content: '确定', loading: submitLoading }"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit="handleSubmit"
      :show-error-message="true"
    >
      <t-form-item label="域名名称" name="domain_name">
        <t-input
          v-model="formData.domain_name"
          placeholder="请输入域名名称，如：example.com"
        />
      </t-form-item>

      <t-form-item label="状态" name="status">
        <t-radio-group v-model="formData.status">
          <t-radio :value="1">启用</t-radio>
          <t-radio :value="0">停用</t-radio>
        </t-radio-group>
      </t-form-item>

      <t-form-item label="域名描述" name="description">
        <t-textarea
          v-model="formData.description"
          placeholder="请输入域名描述"
          :maxlength="500"
        />
      </t-form-item>
    </t-form>
  </t-dialog>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { Plus, RefreshCw, Search, RotateCcw } from 'lucide-vue-next'
import {
  getDomainList,
  createDomain,
  updateDomain,
  deleteDomain,
  batchDeleteDomains,
  updateDomainStatus
} from '@/api'

// 响应式数据
const loading = ref(false)
const domainData = ref([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const editingId = ref(null)
const selectedRowKeys = ref([])

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 表单数据
const formData = reactive({
  domain_name: '',
  status: 1,
  description: ''
})

// 筛选表单数据
const filterForm = reactive({
  domain_name: '',
  status: ''
})

// 表单验证规则
const formRules = {
  domain_name: [
    { required: true, message: '请输入域名名称', type: 'error', trigger: ['blur', 'change'] },
    {
      pattern: /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/,
      message: '请输入有效的域名格式',
      type: 'error',
      trigger: ['blur', 'change']
    }
  ]
}

// 表格列配置
const columns = [
  {
    colKey: 'row-select',
    type: 'multiple-select',
    width: 50
  },
  {
    colKey: 'domain_name',
    title: '域名名称',
    width: 250,
    ellipsis: true
  },
  {
    colKey: 'status',
    title: '状态',
    width: 100,
    cell: 'status'
  },
  {
    colKey: 'description',
    title: '描述',
    width: 300,
    ellipsis: true,
    cell: (h, { row }) => row.description || '-'
  },
  {
    colKey: 'create_time',
    title: '创建时间',
    width: 180,
    ellipsis: true
  },
  {
    colKey: 'update_time',
    title: '更新时间',
    width: 180,
    ellipsis: true
  },
  {
    colKey: 'action',
    title: '操作',
    width: 200,
    cell: 'action'
  }
]

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑域名' : '新增域名'
})

// 方法
const loadDomainData = async () => {
  loading.value = true
  try {
    console.log('🔄 开始加载域名数据...')
    const params = {
      page: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    }

    // 添加筛选条件
    if (filterForm.domain_name) params.domain_name = filterForm.domain_name
    if (filterForm.status !== '') params.status = filterForm.status

    const response = await getDomainList(params)
    console.log('📊 域名数据响应:', response)

    if (response.code === 200) {
      domainData.value = response.data || []
      paginationConfig.total = response.total || 0
      console.log('✅ 域名数据加载成功:', {
        总数: domainData.value.length,
        域名列表: domainData.value.map(item => ({
          id: item.id,
          domain_name: item.domain_name,
          status: item.status
        }))
      })
    } else {
      console.error('❌ 域名数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取域名数据失败')
      domainData.value = []
    }
  } catch (error) {
    console.error('❌ 加载域名数据异常:', error)
    await MessagePlugin.error('获取域名数据失败，请稍后重试')
    domainData.value = []
  } finally {
    loading.value = false
  }
}

// 分页变化处理
const handlePageChange = (pageInfo) => {
  console.log('📄 分页变化:', pageInfo)
  paginationConfig.current = pageInfo.current
  paginationConfig.pageSize = pageInfo.pageSize
  loadDomainData()
}

// 搜索处理
const handleSearch = () => {
  console.log('🔍 执行搜索:', filterForm)
  paginationConfig.current = 1 // 重置到第一页
  loadDomainData()
}

// 重置搜索
const handleReset = () => {
  console.log('🔄 重置搜索条件')
  Object.assign(filterForm, {
    domain_name: '',
    status: ''
  })
  paginationConfig.current = 1
  loadDomainData()
}

// 新增域名
const handleAdd = () => {
  console.log('➕ 新增域名')
  editingId.value = null
  resetForm()
  dialogVisible.value = true
}

// 编辑域名
const handleEdit = (row) => {
  console.log('✏️ 编辑域名:', row)
  editingId.value = row.id
  Object.assign(formData, {
    domain_name: row.domain_name,
    status: row.status,
    description: row.description || ''
  })
  dialogVisible.value = true
}

// 删除域名
const handleDelete = (row) => {
  // 显示确认对话框
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除域名"${row.domain_name}"吗？此操作不可恢复。`,
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        console.log('🗑️ 开始删除域名:', row.id)
        const response = await deleteDomain(row.id)

        if (response.code === 200) {
          await MessagePlugin.success('域名删除成功')
          await loadDomainData() // 重新加载数据
          dialog.destroy() // 关闭对话框
        } else {
          await MessagePlugin.error(response.message || '删除失败')
          dialog.destroy() // 关闭对话框
        }
      } catch (error) {
        console.error('❌ 删除域名失败:', error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error('删除失败，请稍后重试')
        }
        dialog.destroy() // 关闭对话框
      }
    },
    onCancel: () => {
      console.log('❌ 取消删除域名')
      dialog.destroy()
    }
  })
}

// 批量删除域名
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    MessagePlugin.warning('请选择要删除的域名')
    return
  }

  // 显示确认对话框
  const dialog = DialogPlugin.confirm({
    header: '确认批量删除',
    body: `确定要删除选中的 ${selectedRowKeys.value.length} 个域名吗？此操作不可恢复。`,
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        console.log('🗑️ 开始批量删除域名:', selectedRowKeys.value)
        const response = await batchDeleteDomains(selectedRowKeys.value)

        if (response.code === 200) {
          await MessagePlugin.success('批量删除成功')
          selectedRowKeys.value = []
          await loadDomainData() // 重新加载数据
          dialog.destroy() // 关闭对话框
        } else {
          await MessagePlugin.error(response.message || '批量删除失败')
          dialog.destroy() // 关闭对话框
        }
      } catch (error) {
        console.error('❌ 批量删除域名失败:', error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error('批量删除失败，请稍后重试')
        }
        dialog.destroy() // 关闭对话框
      }
    },
    onCancel: () => {
      console.log('❌ 取消批量删除域名')
      dialog.destroy()
    }
  })
}

// 切换域名状态
const handleToggleStatus = (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const action = newStatus === 1 ? '启用' : '停用'

  // 显示确认对话框
  const dialog = DialogPlugin.confirm({
    header: `确认${action}`,
    body: `确定要${action}域名"${row.domain_name}"吗？`,
    confirmBtn: `确定${action}`,
    cancelBtn: '取消',
    theme: 'info',
    onConfirm: async () => {
      try {
        console.log(`🔄 开始${action}域名:`, row.id, '新状态:', newStatus)
        const response = await updateDomainStatus(row.id, newStatus)

        if (response.code === 200) {
          await MessagePlugin.success(`域名${action}成功`)
          await loadDomainData() // 重新加载数据
          dialog.destroy() // 关闭对话框
        } else {
          await MessagePlugin.error(response.message || `${action}失败`)
          dialog.destroy() // 关闭对话框
        }
      } catch (error) {
        console.error(`❌ ${action}域名失败:`, error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error(`${action}失败，请稍后重试`)
        }
        dialog.destroy() // 关闭对话框
      }
    },
    onCancel: () => {
      console.log(`❌ 取消${action}域名`)
      dialog.destroy()
    }
  })
}

// 表格选择变化
const handleSelectChange = (value) => {
  selectedRowKeys.value = value
  console.log('📋 选中的域名:', value)
}

// 提交表单
const handleSubmit = async () => {
  // 验证表单
  const valid = await formRef.value?.validate()
  if (!valid) {
    console.log('❌ 表单验证失败')
    return
  }

  submitLoading.value = true
  try {
    console.log('📝 提交域名数据:', formData)

    let response
    if (editingId.value) {
      // 编辑域名
      console.log('✏️ 更新域名:', editingId.value)
      response = await updateDomain(editingId.value, formData)
    } else {
      // 新增域名
      console.log('➕ 创建新域名')
      response = await createDomain(formData)
    }

    if (response.code === 200) {
      const action = editingId.value ? '更新' : '创建'
      await MessagePlugin.success(`域名${action}成功`)
      dialogVisible.value = false
      resetForm()
      await loadDomainData()
    } else {
      await MessagePlugin.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('❌ 提交失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('操作失败，请稍后重试')
    }
  } finally {
    submitLoading.value = false
  }
}

// 取消对话框
const handleCancel = () => {
  console.log('❌ 取消操作')
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    domain_name: '',
    status: 1,
    description: ''
  })
  editingId.value = null

  // 清除表单验证状态
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 组件挂载时加载数据
onMounted(() => {
  console.log('🚀 域名管理页面已挂载，开始加载数据')
  loadDomainData()
})
</script>

<style lang="less" scoped>
.domain-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-banner {
  width: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  padding: 40px 0;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.banner-content {
  color: white;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.page-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e7e7e7;

  :deep(.t-form) {
    .t-form-item {
      margin-bottom: 16px;
    }

    .t-form-item:last-child {
      margin-bottom: 0;
    }
  }
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e7e7e7;
}

.table-container {
  margin-top: 16px;
}

.text-placeholder {
  color: #999;
  font-style: italic;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-content {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .action-bar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
