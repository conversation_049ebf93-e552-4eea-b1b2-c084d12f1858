<template>
  <div class="role-management">
    <!-- 蓝色横幅 - 100%宽度 -->
    <div class="header-banner">
      <div class="container">
        <div class="banner-content">
          <h1 class="page-title">角色管理</h1>
          <p class="page-description">管理系统角色，配置角色权限和菜单分配</p>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="container">
      <div class="page-content">
        <!-- 操作栏 -->
        <div class="action-bar">
          <t-button theme="primary" @click="handleAdd">
            <template #icon>
              <Plus :size="16" />
            </template>
            新增角色
          </t-button>
          <t-button variant="outline" @click="handleRefresh">
            <template #icon>
              <RefreshCw :size="16" />
            </template>
            刷新
          </t-button>
        </div>

        <!-- 角色表格 -->
        <div class="table-container">
          <t-enhanced-table
            :data="roleData"
            :columns="columns"
            :loading="loading"
            row-key="id"
            :pagination="paginationConfig"
            stripe
            hover
            @page-change="handlePageChange"
          >
            <!-- 状态列 -->
            <template #status="{ row }">
              <t-tag :theme="row.status === 1 ? 'success' : 'danger'" variant="light">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </t-tag>
            </template>

            <!-- 操作列 -->
            <template #action="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                  编辑
                </t-button>
                <t-button theme="success" variant="text" size="small" @click="handleAssignMenu(row)">
                  分配菜单
                </t-button>
                <t-button theme="warning" variant="text" size="small" @click="handleAssignPermission(row)">
                  分配权限
                </t-button>

                <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-enhanced-table>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增/编辑角色对话框 -->
  <t-dialog
    v-model:visible="dialogVisible"
    :header="dialogTitle"
    width="900px"
    :confirm-btn="{ content: '确定', loading: submitLoading }"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <t-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      @submit="handleSubmit"
      :show-error-message="true"
    >
      <t-form-item label="角色名称" name="name">
        <t-input
          v-model="formData.name"
          placeholder="请输入角色名称"
        />
      </t-form-item>

      <t-form-item label="角色代码" name="code">
        <t-input
          v-model="formData.code"
          placeholder="请输入角色代码（英文标识）"
        />
      </t-form-item>

      <t-form-item label="角色描述" name="description">
        <t-textarea
          v-model="formData.description"
          placeholder="请输入角色描述"
          :maxlength="500"
        />
      </t-form-item>

      <t-form-item label="状态" name="status">
        <t-radio-group v-model="formData.status">
          <t-radio :value="1">启用</t-radio>
          <t-radio :value="0">禁用</t-radio>
        </t-radio-group>
      </t-form-item>
    </t-form>
  </t-dialog>

  <!-- 分配菜单对话框 -->
  <t-dialog
    v-model:visible="menuDialogVisible"
    header="分配菜单"
    width="800px"
    :confirm-btn="{ content: '确定', loading: menuSubmitLoading }"
    @confirm="handleMenuSubmit"
    @cancel="handleMenuCancel"
  >
    <div class="menu-assignment">
      <div class="role-info">
        <h4>为角色 "{{ currentRole?.name }}" 分配菜单</h4>
        <p class="text-placeholder">请选择该角色可以访问的菜单项</p>
      </div>

      <div class="menu-tree-container">
        <t-tree
          ref="menuTreeRef"
          :data="menuTreeData"
          :keys="treeKeys"
          checkable
          expand-all
          v-model:value="checkedMenuIds"
          @change="handleMenuCheck"
        />
      </div>
    </div>
  </t-dialog>

  <!-- 分配权限对话框 -->
  <t-dialog
    v-model:visible="permissionDialogVisible"
    header="分配权限"
    width="800px"
    :confirm-btn="{ content: '确定', loading: permissionSubmitLoading }"
    @confirm="handlePermissionSubmit"
    @cancel="handlePermissionCancel"
  >
    <div class="permission-assignment">
      <div class="role-info">
        <h4>为角色 "{{ currentPermissionRole?.name }}" 分配权限</h4>
        <p class="text-placeholder">请选择该角色拥有的权限</p>
      </div>

      <div class="permission-groups" v-if="availablePermissions.length > 0">
        <div v-for="group in availablePermissions" :key="group.group_code" class="permission-group">
          <div class="group-header">
            <t-checkbox
              :checked="isGroupFullySelected(group)"
              :indeterminate="isGroupPartiallySelected(group)"
              @change="(checked) => toggleGroupSelection(group, checked)"
            >
              <t-tag :theme="getPermissionTheme(group.group_code)" variant="light" size="small">
                {{ group.group_name }}
              </t-tag>
            </t-checkbox>
          </div>
          <div class="group-permissions">
            <t-checkbox-group v-model="selectedPermissions" class="permission-checkboxes">
              <div
                v-for="permission in group.permissions"
                :key="permission.id"
                class="permission-item"
              >
                <t-checkbox
                  :value="permission.id"
                  class="permission-checkbox"
                >
                  {{ permission.name }}
                </t-checkbox>
              </div>
            </t-checkbox-group>
          </div>
        </div>
      </div>
      <div v-else class="loading-permissions">
        <t-loading text="加载权限中..." />
      </div>
    </div>
  </t-dialog>


</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { Plus, RefreshCw } from 'lucide-vue-next'
import {
  getRoleList,
  createRole,
  updateRole,
  deleteRole,
  getMenuList,
  getRoleMenus,
  assignMenusToRole,
  getPermissionGroups,
  getRolePermissions,
  assignPermissionsToRole
} from '@/api'

// 响应式数据
const loading = ref(false)
const roleData = ref([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const editingId = ref(null)

// 菜单分配相关
const menuDialogVisible = ref(false)
const menuSubmitLoading = ref(false)
const menuTreeRef = ref(null)
const currentRole = ref(null)
const menuTreeData = ref([])
const checkedMenuIds = ref([])

// 权限分配相关
const permissionDialogVisible = ref(false)
const permissionSubmitLoading = ref(false)
const currentPermissionRole = ref(null)
const availablePermissions = ref([])
const selectedPermissions = ref([])



// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 表单数据
const formData = reactive({
  name: '',
  code: '',
  description: '',
  status: 1
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称', type: 'error', trigger: ['blur', 'change'] }
  ],
  code: [
    { required: true, message: '请输入角色代码', type: 'error', trigger: ['blur', 'change'] },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '角色代码只能包含字母、数字和下划线，且以字母开头', type: 'error', trigger: ['blur', 'change'] }
  ]

}

// 表格列配置
const columns = [
  {
    colKey: 'name',
    title: '角色名称',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'code',
    title: '角色代码',
    width: 150,
    ellipsis: true
  },
  {
    colKey: 'description',
    title: '角色描述',
    width: 200,
    ellipsis: true
  },

  {
    colKey: 'status',
    title: '状态',
    width: 100,
    cell: 'status'
  },
  {
    colKey: 'create_time',
    title: '创建时间',
    width: 180,
    ellipsis: true
  },
  {
    colKey: 'action',
    title: '操作',
    width: 200,
    cell: 'action'
  }
]

// 树形组件配置
const treeKeys = {
  value: 'id',
  label: 'title',
  children: 'children'
}

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑角色' : '新增角色'
})

// 方法
const loadRoleData = async () => {
  loading.value = true
  try {
    console.log('🔄 开始加载角色数据...')
    const response = await getRoleList({
      page: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    })
    console.log('📊 角色数据响应:', response)

    if (response.code === 200) {
      roleData.value = response.data || []
      paginationConfig.total = response.total || 0
      console.log('✅ 角色数据加载成功:', {
        总数: roleData.value.length,
        角色列表: roleData.value.map(item => ({
          id: item.id,
          name: item.name,
          code: item.code,
          status: item.status
        }))
      })
    } else {
      console.error('❌ 角色数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取角色数据失败')
      roleData.value = []
    }
  } catch (error) {
    console.error('❌ 加载角色数据异常:', error)
    await MessagePlugin.error('加载角色数据失败，请检查网络连接')
    roleData.value = []
  } finally {
    loading.value = false
  }
}

const loadMenuData = async () => {
  try {
    console.log('🔄 开始加载菜单数据...')
    const response = await getMenuList()
    console.log('📊 菜单数据响应:', response)

    if (response.code === 200) {
      menuTreeData.value = response.data || []
      console.log('✅ 菜单数据加载成功')
    } else {
      console.error('❌ 菜单数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取菜单数据失败')
      menuTreeData.value = []
    }
  } catch (error) {
    console.error('❌ 加载菜单数据异常:', error)
    await MessagePlugin.error('加载菜单数据失败，请检查网络连接')
    menuTreeData.value = []
  }
}

const handlePageChange = (pageInfo) => {
  paginationConfig.current = pageInfo.current
  paginationConfig.pageSize = pageInfo.pageSize
  loadRoleData()
}

const handleAdd = () => {
  resetForm()
  editingId.value = null
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  Object.assign(formData, {
    name: row.name,
    code: row.code,
    description: row.description,
    status: row.status
  })
  editingId.value = row.id
  dialogVisible.value = true
}

const handleDelete = (row) => {
  // 显示确认对话框
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除角色"${row.name}"吗？此操作不可恢复。`,
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        console.log('🗑️ 开始删除角色:', row.id)
        const response = await deleteRole(row.id)

        if (response.code === 200) {
          await MessagePlugin.success('角色删除成功')
          await loadRoleData() // 重新加载数据
          dialog.destroy() // 关闭对话框
        } else {
          await MessagePlugin.error(response.message || '删除失败')
          dialog.destroy() // 关闭对话框
        }
      } catch (error) {
        console.error('❌ 删除角色失败:', error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error('删除失败，请稍后重试')
        }
        dialog.destroy() // 关闭对话框
      }
    },
    onCancel: () => {
      console.log('🚫 用户取消删除操作')
      dialog.destroy() // 关闭对话框
    }
  })
}

const handleRefresh = () => {
  loadRoleData()
}

const handleSubmit = async () => {
  const valid = await formRef.value?.validate()
  if (!valid) return

  submitLoading.value = true
  try {
    console.log('📝 提交角色数据:', formData)


    let response
    if (editingId.value) {
      // 编辑角色
      console.log('✏️ 更新角色:', editingId.value)
      response = await updateRole(editingId.value, formData)
    } else {
      // 新增角色
      console.log('➕ 创建新角色')
      response = await createRole(formData)
    }

    if (response.code === 200) {
      const action = editingId.value ? '更新' : '创建'
      await MessagePlugin.success(`角色${action}成功`)
      dialogVisible.value = false
      resetForm() // 重置表单
      await loadRoleData() // 重新加载数据
    } else {
      await MessagePlugin.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('❌ 提交失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('操作失败，请稍后重试')
    }
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  // 重置表单数据
  Object.assign(formData, {
    name: '',
    code: '',
    description: '',
    status: 1
  })
  // 使用 TDesign 表单的 reset() 方法重置表单
  formRef.value?.reset()
  // 清除验证状态
  formRef.value?.clearValidate()
}

// 菜单分配相关方法
const handleAssignMenu = async (row) => {
  currentRole.value = row

  // 先清空之前的选中状态
  checkedMenuIds.value = []

  // 显示对话框
  menuDialogVisible.value = true

  // 先加载菜单数据
  await loadMenuData()

  // 等待菜单数据加载完成后再加载角色菜单
  await nextTick()

  // 加载角色已有的菜单
  await loadRoleMenus(row.id)
}

const loadRoleMenus = async (roleId) => {
  try {
    console.log('🔄 开始加载角色菜单:', roleId)
    const response = await getRoleMenus(roleId)
    console.log('📊 角色菜单响应:', response)

    if (response.code === 200) {
      // 提取所有菜单ID（包括父级和子级）
      const menuIds = extractMenuIds(response.data || [])
      console.log('📋 提取的菜单ID列表:', menuIds)
      console.log('🌳 当前菜单树数据:', menuTreeData.value)

      checkedMenuIds.value = menuIds
      console.log('✅ 角色菜单加载成功，设置选中状态:', checkedMenuIds.value)

      // 等待下一个tick确保树组件已更新
      await nextTick()
      console.log('🔄 nextTick后的选中状态:', checkedMenuIds.value)
    } else {
      console.error('❌ 角色菜单加载失败:', response.message)
      checkedMenuIds.value = []
    }
  } catch (error) {
    console.error('❌ 加载角色菜单异常:', error)
    checkedMenuIds.value = []
  }
}

// 递归提取菜单ID
const extractMenuIds = (menus) => {
  const ids = []
  const traverse = (menuList) => {
    menuList.forEach(menu => {
      ids.push(menu.id)
      if (menu.children && menu.children.length > 0) {
        traverse(menu.children)
      }
    })
  }
  traverse(menus)
  return ids
}

const handleMenuCheck = (checkedKeys, context) => {
  console.log('菜单选择变化:', checkedKeys, context)
  checkedMenuIds.value = checkedKeys
}

const handleMenuSubmit = async () => {
  if (!currentRole.value) return

  menuSubmitLoading.value = true
  try {
    console.log('📝 提交菜单分配:', {
      roleId: currentRole.value.id,
      menuIds: checkedMenuIds.value
    })

    const response = await assignMenusToRole(currentRole.value.id, checkedMenuIds.value)

    if (response.code === 200) {
      await MessagePlugin.success('菜单分配成功')
      menuDialogVisible.value = false
    } else {
      await MessagePlugin.error(response.message || '菜单分配失败')
    }
  } catch (error) {
    console.error('❌ 菜单分配失败:', error)
    if (error.response?.data?.message) {
      await MessagePlugin.error(error.response.data.message)
    } else {
      await MessagePlugin.error('菜单分配失败，请稍后重试')
    }
  } finally {
    menuSubmitLoading.value = false
  }
}

const handleMenuCancel = () => {
  menuDialogVisible.value = false
  currentRole.value = null
  checkedMenuIds.value = []
}

// 权限分配相关方法
const handleAssignPermission = async (row) => {
  currentPermissionRole.value = row
  permissionDialogVisible.value = true

  // 加载所有可用权限
  await loadAvailablePermissions()

  // 加载角色当前权限
  await loadRolePermissions(row.id)
}

const loadAvailablePermissions = async () => {
  try {
    const response = await getPermissionGroups()
    if (response.code === 200) {
      availablePermissions.value = response.data
    } else {
      console.error('加载权限失败:', response.message)
      availablePermissions.value = []
    }
  } catch (error) {
    console.error('加载权限异常:', error)
    availablePermissions.value = []
  }
}

const loadRolePermissions = async (roleId) => {
  try {
    const response = await getRolePermissions(roleId)
    if (response.code === 200) {
      // response.data 是权限代码数组，需要转换为权限ID数组
      const permissionCodes = response.data
      const permissionIds = []

      // 遍历所有权限分组，找到对应的权限ID
      availablePermissions.value.forEach(group => {
        group.permissions.forEach(permission => {
          if (permissionCodes.includes(permission.code)) {
            permissionIds.push(permission.id)
          }
        })
      })

      selectedPermissions.value = permissionIds
    } else {
      console.error('加载角色权限失败:', response.message)
      selectedPermissions.value = []
    }
  } catch (error) {
    console.error('加载角色权限异常:', error)
    selectedPermissions.value = []
  }
}

const handlePermissionSubmit = async () => {
  if (!currentPermissionRole.value) return

  permissionSubmitLoading.value = true
  try {
    const response = await assignPermissionsToRole(currentPermissionRole.value.id, selectedPermissions.value)
    if (response.code === 200) {
      await MessagePlugin.success('权限分配成功')
      permissionDialogVisible.value = false
      await loadRoleData() // 重新加载角色数据
    } else {
      await MessagePlugin.error(response.message || '权限分配失败')
    }
  } catch (error) {
    console.error('权限分配失败:', error)
    await MessagePlugin.error('权限分配失败')
  } finally {
    permissionSubmitLoading.value = false
  }
}

const handlePermissionCancel = () => {
  permissionDialogVisible.value = false
  currentPermissionRole.value = null
  selectedPermissions.value = []
  availablePermissions.value = []
}

// 权限选择相关方法
const isGroupFullySelected = (group) => {
  if (!selectedPermissions.value || selectedPermissions.value.length === 0) return false
  return group.permissions.every(permission => selectedPermissions.value.includes(permission.id))
}

const isGroupPartiallySelected = (group) => {
  if (!selectedPermissions.value || selectedPermissions.value.length === 0) return false
  const selectedCount = group.permissions.filter(permission =>
    selectedPermissions.value.includes(permission.id)
  ).length
  return selectedCount > 0 && selectedCount < group.permissions.length
}

const toggleGroupSelection = (group, checked) => {
  if (!selectedPermissions.value) {
    selectedPermissions.value = []
  }

  if (checked) {
    // 选中整个组
    group.permissions.forEach(permission => {
      if (!selectedPermissions.value.includes(permission.id)) {
        selectedPermissions.value.push(permission.id)
      }
    })
  } else {
    // 取消选中整个组
    group.permissions.forEach(permission => {
      const index = selectedPermissions.value.indexOf(permission.id)
      if (index > -1) {
        selectedPermissions.value.splice(index, 1)
      }
    })
  }
}

const getPermissionTheme = (groupCode) => {
  const themes = {
    user: 'primary',
    role: 'success',
    menu: 'warning',
    permission: 'danger',
    system: 'default'
  }
  return themes[groupCode] || 'default'
}





// 生命周期
onMounted(() => {
  loadRoleData()
})
</script>

<style lang="less" scoped>
.role-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-banner {
  width: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  padding: 40px 0;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.banner-content {
  color: white;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.page-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e7e7e7;
}

.table-container {
  margin-top: 16px;
}

.text-placeholder {
  color: #999;
  font-style: italic;
}







// 菜单分配对话框样式
.menu-assignment {
  .role-info {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
    }

    .text-placeholder {
      margin: 0;
      font-size: 14px;
    }
  }

  .menu-tree-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    padding: 16px;
    background: white;
  }
}

// 权限分配对话框样式
.permission-assignment {
  .role-info {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;

    h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 16px;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .permission-groups {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    padding: 16px;
    background: #fafafa;
  }

  .permission-group {
    background: white;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    &:last-child {
      margin-bottom: 0;
    }

    .group-header {
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e7e7e7;

      :deep(.t-checkbox) {
        font-weight: 600;

        .t-checkbox__label {
          font-size: 14px;
        }
      }
    }

    .group-permissions {
      .permission-checkboxes {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .permission-item {
          padding: 6px 8px;
          border-radius: 4px;
          transition: background-color 0.2s;

          &:hover {
            background-color: #f0f0f0;
          }

          .permission-checkbox {
            margin-bottom: 0;
            width: 100%;

            :deep(.t-checkbox__label) {
              font-size: 13px;
              color: #666;
              width: 100%;
            }
          }
        }
      }
    }
  }

  .loading-permissions {
    text-align: center;
    padding: 40px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-content {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .action-bar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
