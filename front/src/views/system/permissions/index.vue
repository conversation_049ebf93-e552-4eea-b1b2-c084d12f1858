<template>
  <div class="permission-management">
    <!-- 蓝色横幅 - 100%宽度 -->
    <div class="header-banner">
      <div class="container">
        <div class="banner-content">
          <h1 class="page-title">权限管理</h1>
          <p class="page-description">管理系统权限配置，控制用户操作权限和访问范围</p>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="container">
      <div class="page-content">
        <!-- 操作栏 -->
        <div class="action-bar">
          <t-button theme="primary" @click="handleAdd">
            <template #icon>
              <Plus :size="16" />
            </template>
            新增权限
          </t-button>
          <t-button variant="outline" @click="handleRefresh">
            <template #icon>
              <RefreshCw :size="16" />
            </template>
            刷新
          </t-button>
        </div>

        <!-- 权限表格 -->
        <div class="table-container">
          <t-enhanced-table
            :data="permissionData"
            :columns="columns"
            :loading="loading"
            row-key="id"
            :pagination="paginationConfig"
            stripe
            hover
            @page-change="handlePageChange"
          >
            <!-- 权限名称列 -->
            <template #name="{ row }">
              <span>{{ row.name }}</span>
              <t-tag
                :theme="getGroupTagTheme(row.group_code)"
                variant="light"
                size="small"
                style="margin-left: 8px;"
              >
                {{ row.group_name }}
              </t-tag>
            </template>

            <!-- 权限代码列 -->
            <template #code="{ row }">
              <t-tag theme="default" variant="outline" size="small">
                {{ row.code }}
              </t-tag>
            </template>

            <!-- 状态列 -->
            <template #status="{ row }">
              <t-tag
                :theme="row.status === 1 ? 'success' : 'danger'"
                variant="light"
                size="small"
              >
                {{ row.status === 1 ? '启用' : '禁用' }}
              </t-tag>
            </template>

            <!-- 创建时间列 -->
            <template #create_time="{ row }">
              {{ formatDateTime(row.create_time) }}
            </template>

            <!-- 操作列 -->
            <template #action="{ row }">
              <t-space>
                <t-button theme="primary" variant="text" size="small" @click="handleEdit(row)">
                  编辑
                </t-button>
                <t-button theme="danger" variant="text" size="small" @click="handleDelete(row)">
                  删除
                </t-button>
              </t-space>
            </template>
          </t-enhanced-table>
        </div>
      </div>
    </div>

    <!-- 新增/编辑权限对话框 -->
    <t-dialog
      v-model:visible="dialogVisible"
      :header="dialogTitle"
      width="600px"
      :confirm-btn="{ content: '确定', loading: submitLoading }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <t-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        @submit="handleSubmit"
      >
        <t-form-item label="权限代码" name="code">
          <t-input
            v-model="formData.code"
            placeholder="请输入权限代码，如：user:add"
          />
        </t-form-item>

        <t-form-item label="权限名称" name="name">
          <t-input
            v-model="formData.name"
            placeholder="请输入权限名称，如：新增用户"
          />
        </t-form-item>

        <t-form-item label="权限描述" name="description">
          <t-textarea
            v-model="formData.description"
            placeholder="请输入权限描述"
            :autosize="{ minRows: 3, maxRows: 5 }"
          />
        </t-form-item>

        <t-form-item label="分组代码" name="group_code">
          <t-input
            v-model="formData.group_code"
            placeholder="请输入分组代码，如：user"
          />
        </t-form-item>

        <t-form-item label="分组名称" name="group_name">
          <t-input
            v-model="formData.group_name"
            placeholder="请输入分组名称，如：用户管理"
          />
        </t-form-item>

        <t-form-item label="排序顺序" name="sort_order">
          <t-input-number
            v-model="formData.sort_order"
            placeholder="请输入排序顺序"
            :min="0"
            :max="9999"
          />
        </t-form-item>

        <t-form-item label="状态" name="status">
          <t-radio-group v-model="formData.status">
            <t-radio :value="1">启用</t-radio>
            <t-radio :value="0">禁用</t-radio>
          </t-radio-group>
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { MessagePlugin, DialogPlugin } from 'tdesign-vue-next'
import { Plus, RefreshCw } from 'lucide-vue-next'
import {
  getPermissionList,
  createPermission,
  updatePermission,
  deletePermission
} from '@/api'

// 响应式数据
const loading = ref(false)
const permissionData = ref([])
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref(null)
const editingId = ref(null)

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showJumper: true,
  showSizer: true,
  pageSizeOptions: [10, 20, 50, 100]
})

// 表单数据
const formData = reactive({
  code: '',
  name: '',
  description: '',
  group_code: '',
  group_name: '',
  sort_order: 0,
  status: 1
})

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入权限代码', type: 'error' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9:_]*$/, message: '权限代码只能包含字母、数字、冒号和下划线，且以字母开头', type: 'error' }
  ],
  name: [
    { required: true, message: '请输入权限名称', type: 'error' }
  ],
  group_code: [
    { required: true, message: '请输入分组代码', type: 'error' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '分组代码只能包含字母、数字和下划线，且以字母开头', type: 'error' }
  ],
  group_name: [
    { required: true, message: '请输入分组名称', type: 'error' }
  ]
}

// 表格列配置
const columns = [
  {
    colKey: 'name',
    title: '权限名称',
    width: 200,
    ellipsis: true
  },
  {
    colKey: 'code',
    title: '权限代码',
    width: 180,
    ellipsis: true
  },
  {
    colKey: 'description',
    title: '权限描述',
    width: 250,
    ellipsis: true
  },
  {
    colKey: 'group_name',
    title: '权限分组',
    width: 120
  },
  {
    colKey: 'sort_order',
    title: '排序',
    width: 80
  },
  {
    colKey: 'status',
    title: '状态',
    width: 80,
    cell: 'status'
  },
  {
    colKey: 'create_time',
    title: '创建时间',
    width: 160,
    ellipsis: true,
    cell: 'create_time'
  },
  {
    colKey: 'action',
    title: '操作',
    width: 150,
    cell: 'action'
  }
]

// 计算属性
const dialogTitle = computed(() => {
  return editingId.value ? '编辑权限' : '新增权限'
})

// 获取分组标签主题色
const getGroupTagTheme = (groupCode) => {
  const themes = {
    'user': 'primary',
    'role': 'success',
    'menu': 'warning',
    'permission': 'danger',
    'system': 'default'
  }
  return themes[groupCode] || 'default'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'

  try {
    const date = new Date(dateTime)
    if (isNaN(date.getTime())) return '-'

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    console.error('日期格式化失败:', error)
    return '-'
  }
}

// 方法
const loadPermissionData = async () => {
  loading.value = true
  try {
    console.log('🔄 开始加载权限数据...')
    const response = await getPermissionList({
      page: paginationConfig.current,
      pageSize: paginationConfig.pageSize
    })
    console.log('📊 权限数据响应:', response)

    if (response.code === 200) {
      permissionData.value = response.data || []
      paginationConfig.total = response.total || 0
      console.log('✅ 权限数据加载成功:', {
        总数: paginationConfig.total,
        当前页: paginationConfig.current,
        权限项: permissionData.value.length
      })
    } else {
      console.error('❌ 权限数据加载失败:', response.message)
      await MessagePlugin.error(response.message || '获取权限数据失败')
      permissionData.value = []
      paginationConfig.total = 0
    }
  } catch (error) {
    console.error('❌ 加载权限数据异常:', error)
    await MessagePlugin.error('加载权限数据失败，请检查网络连接')
    permissionData.value = []
    paginationConfig.total = 0
  } finally {
    loading.value = false
  }
}

const handleAdd = () => {
  resetForm()
  editingId.value = null
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  Object.assign(formData, row)
  editingId.value = row.id
  dialogVisible.value = true
}

const handleDelete = (row) => {
  // 显示确认对话框
  const dialog = DialogPlugin.confirm({
    header: '确认删除',
    body: `确定要删除权限"${row.name}"吗？此操作不可恢复。`,
    confirmBtn: '确定删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      try {
        console.log('🗑️ 开始删除权限:', row.id)
        const response = await deletePermission(row.id)
        console.log('🗑️ 删除权限响应:', response)

        if (response.code === 200) {
          await MessagePlugin.success('权限删除成功')
          await loadPermissionData() // 重新加载数据
          dialog.destroy() // 关闭对话框
        } else {
          await MessagePlugin.error(response.message || '删除权限失败')
          dialog.destroy() // 关闭对话框
        }
      } catch (error) {
        console.error('❌ 删除权限失败:', error)
        if (error.response?.data?.message) {
          await MessagePlugin.error(error.response.data.message)
        } else {
          await MessagePlugin.error('删除失败，请稍后重试')
        }
        dialog.destroy() // 关闭对话框
      }
    },
    onCancel: () => {
      console.log('🚫 用户取消删除操作')
      dialog.destroy() // 关闭对话框
    }
  })
}

const handleRefresh = () => {
  loadPermissionData()
}

const handlePageChange = (pageInfo) => {
  paginationConfig.current = pageInfo.current
  paginationConfig.pageSize = pageInfo.pageSize
  loadPermissionData()
}

const handleSubmit = async () => {
  // 验证表单
  const valid = await formRef.value?.validate()
  if (!valid) {
    return
  }

  submitLoading.value = true
  try {
    console.log('💾 开始提交权限数据:', formData)

    let response
    if (editingId.value) {
      // 编辑权限
      response = await updatePermission(editingId.value, formData)
      console.log('✏️ 编辑权限响应:', response)
    } else {
      // 新增权限
      response = await createPermission(formData)
      console.log('➕ 新增权限响应:', response)
    }

    if (response.code === 200) {
      await MessagePlugin.success(editingId.value ? '权限更新成功' : '权限创建成功')
      dialogVisible.value = false
      await loadPermissionData()
    } else {
      await MessagePlugin.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('❌ 提交权限数据异常:', error)
    await MessagePlugin.error('操作失败，请检查网络连接')
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  // 重置表单数据
  Object.assign(formData, {
    code: '',
    name: '',
    description: '',
    group_code: '',
    group_name: '',
    sort_order: 0,
    status: 1
  })
  // 使用 TDesign 表单的 reset() 方法重置表单
  formRef.value?.reset()
}

// 生命周期
onMounted(() => {
  loadPermissionData()
})
</script>

<style lang="less" scoped>
.permission-management {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 24px;
}

.header-banner {
  width: 100%;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
  padding: 40px 0;
  margin-bottom: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.banner-content {
  color: white;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-description {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.page-content {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-bar {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e7e7e7;
}

.table-container {
  margin-top: 16px;
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .page-content {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .action-bar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
