<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>无限邮箱系统 - 个人中心</title>
    <script src="https://res.gemcoder.com/js/reload.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link
            href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css"
            rel="stylesheet"
    />
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#165DFF',
                        secondary: '#4080FF',
                        success: '#00B42A',
                        warning: '#FF7D00',
                        danger: '#F53F3F',
                        info: '#86909C',
                        light: '#F2F3F5',
                        dark: '#1D2129',
                        'primary-light': '#E8F3FF',
                        'secondary-light': '#F0F7FF'
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif']
                    },
                    boxShadow: {
                        'card': '0 4px 16px rgba(0, 0, 0, 0.08)',
                        'card-hover': '0 8px 24px rgba(0, 0, 0, 0.12)'
                    }
                }
            }
        };
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-shadow {
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .transition-custom {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }
            .bg-gradient-primary {
                background: linear-gradient(135deg, #165DFF 0%, #4080FF 100%);
            }
        }
    </style>
</head>
<body class="font-inter bg-gray-50 text-dark min-h-screen">
<div class="container mx-auto px-4 py-8 max-w-7xl">
    <!-- 页面标题横幅 -->
    <div
            class="bg-gradient-primary rounded-2xl p-8 mb-8 text-white shadow-lg w-full"
    >
        <h1 class="text-[clamp(1.75rem,3vw,2.5rem)] font-bold mb-2 text-shadow">
            个人中心
        </h1>
        <p class="text-white/90 max-w-2xl">
            管理您的账户信息、偏好设置和安全选项
        </p>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 左侧个人信息卡片 -->
        <div class="lg:col-span-1">
            <div
                    class="bg-white rounded-xl shadow-card p-6 transition-custom hover:shadow-card-hover"
            >
                <!-- 个人头像 -->
                <div class="flex flex-col items-center mb-6">
                    <div class="relative mb-4">
                        <div
                                class="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg"
                        >
                            <img
                                    alt="用户头像"
                                    class="w-full h-full object-cover"
                                    src="https://design.gemcoder.com/staticResource/echoAiSystemImages/3acb85c31c2c2642e3d65a2bfa801c11.png"
                            />
                        </div>
                        <button
                                class="absolute bottom-0 right-0 bg-primary text-white rounded-full w-10 h-10 flex items-center justify-center shadow-md hover:bg-primary/90 transition-custom"
                        >
                            <i class="fas fa-camera"> </i>
                        </button>
                    </div>
                    <h2 class="text-xl font-bold">张明</h2>
                    <p class="text-info text-sm"><EMAIL></p>
                    <div class="mt-2 flex items-center text-xs text-success">
                        <i class="fas fa-circle mr-1"> </i>
                        <span> 已验证邮箱 </span>
                    </div>
                </div>
                <!-- 统计信息 -->
                <div class="grid grid-cols-1 gap-2 text-center mb-6">
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold text-primary">128</div>
                        <div class="text-sm text-info">收件箱</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 右侧内容区域 -->
        <div class="lg:col-span-3 space-y-8">
            <!-- 个人资料设置 -->
            <div
                    class="bg-white rounded-xl shadow-card p-6 transition-custom hover:shadow-card-hover"
            >
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">个人资料</h2>
                    <button
                            class="text-primary hover:text-primary/80 transition-custom flex items-center"
                            id="edit-profile-btn"
                    >
                        <i class="fas fa-edit mr-1"> </i>
                        <span> 编辑 </span>
                    </button>
                </div>
                <form class="space-y-6" id="profile-form">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                姓名
                            </label>
                            <input
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-custom"
                                    disabled
                                    type="text"
                                    value="张明"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                昵称
                            </label>
                            <input
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-custom"
                                    disabled
                                    type="text"
                                    value="明哥"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                电子邮箱
                            </label>
                            <input
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-custom"
                                    disabled
                                    type="email"
                                    value="<EMAIL>"
                            />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                手机号码
                            </label>
                            <input
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-custom"
                                    disabled
                                    type="tel"
                                    value="138****5678"
                            />
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">
                                个人简介
                            </label>
                            <textarea
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition-custom resize-none"
                                    disabled
                                    rows="3"
                            >
        热爱技术的前端开发工程师，喜欢探索新的技术和工具。
    </textarea
    >
                        </div>
                    </div>
                    <div
                            class="hidden flex justify-end space-x-4 pt-4 border-t border-gray-100"
                            id="profile-form-actions"
                    >
                        <button
                                class="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-custom"
                                id="cancel-edit-btn"
                                type="button"
                        >
                            取消
                        </button>
                        <button
                                class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-custom"
                                type="submit"
                        >
                            保存更改
                        </button>
                    </div>
                </form>
            </div>
            <!-- 已添加邮箱 -->
            <div
                    class="bg-white rounded-xl shadow-card p-6 transition-custom hover:shadow-card-hover"
            >
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">已添加邮箱</h2>
                    <button
                            class="text-primary hover:text-primary/80 transition-custom flex items-center"
                    >
                        <i class="fas fa-plus mr-1"> </i>
                        <span> 添加邮箱 </span>
                    </button>
                </div>
                <div class="space-y-4">
                    <div
                            class="flex justify-between items-center p-[calc(1rem-.7px)] border border-primary rounded-lg bg-primary-light/3"
                    >
                        <!-- 使用calc修复1px边框导致hover错位 -->
                        <div class="flex items-center">
                            <div
                                    class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary mr-4"
                            >
                                <i class="fas fa-envelope"> </i>
                            </div>
                            <div>
                                <h3 class="font-medium"><EMAIL></h3>
                                <p class="text-sm text-info">主邮箱 · 已验证</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button
                                    class="text-gray-500 hover:text-gray-700 transition-custom p-2 rounded-full hover:bg-gray-100"
                            >
                                <i class="fas fa-edit"> </i>
                            </button>
                            <button
                                    class="text-gray-500 hover:text-gray-700 transition-custom p-2 rounded-full hover:bg-gray-100"
                            >
                                <i class="fas fa-ellipsis-v"> </i>
                            </button>
                        </div>
                    </div>
                    <div
                            class="flex justify-between items-center p-[calc(1rem-.7px)] border border-gray-200 rounded-lg hover:border-primary/50 hover:bg-primary-light/20 transition-custom"
                    >
                        <!-- 使用calc修复1px边框导致hover错位 -->
                        <div class="flex items-center">
                            <div
                                    class="w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center text-secondary mr-4"
                            >
                                <i class="fas fa-envelope"> </i>
                            </div>
                            <div>
                                <h3 class="font-medium"><EMAIL></h3>
                                <p class="text-sm text-info">工作邮箱 · 已验证</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button
                                    class="text-gray-500 hover:text-gray-700 transition-custom p-2 rounded-full hover:bg-gray-100"
                            >
                                <i class="fas fa-edit"> </i>
                            </button>
                            <button
                                    class="text-gray-500 hover:text-gray-700 transition-custom p-2 rounded-full hover:bg-gray-100"
                            >
                                <i class="fas fa-ellipsis-v"> </i>
                            </button>
                        </div>
                    </div>
                    <div
                            class="flex justify-between items-center p-[calc(1rem-.7px)] border border-gray-200 rounded-lg hover:border-primary/50 hover:bg-primary-light/20 transition-custom"
                    >
                        <!-- 使用calc修复1px边框导致hover错位 -->
                        <div class="flex items-center">
                            <div
                                    class="w-10 h-10 rounded-full bg-warning/10 flex items-center justify-center text-warning mr-4"
                            >
                                <i class="fas fa-envelope"> </i>
                            </div>
                            <div>
                                <h3 class="font-medium"><EMAIL></h3>
                                <p class="text-sm text-info">个人邮箱 · 已验证</p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button
                                    class="text-gray-500 hover:text-gray-700 transition-custom p-2 rounded-full hover:bg-gray-100"
                            >
                                <i class="fas fa-edit"> </i>
                            </button>
                            <button
                                    class="text-gray-500 hover:text-gray-700 transition-custom p-2 rounded-full hover:bg-gray-100"
                            >
                                <i class="fas fa-ellipsis-v"> </i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
<script>
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function () {
        initProfileForm();
    });

    // 初始化个人资料表单编辑功能
    function initProfileForm() {
        var editBtn = document.getElementById('edit-profile-btn');
        var cancelBtn = document.getElementById('cancel-edit-btn');
        var form = document.getElementById('profile-form');
        var formActions = document.getElementById('profile-form-actions');
        var inputs = form.querySelectorAll('input, textarea');
        editBtn.addEventListener('click', function () {
            inputs.forEach(function (input) {
                input.disabled = false;
                input.classList.remove('border-gray-300');
                input.classList.add('border-primary/50');
            });
            formActions.classList.remove('hidden');
            editBtn.classList.add('hidden');
        });
        cancelBtn.addEventListener('click', function () {
            inputs.forEach(function (input) {
                input.disabled = true;
                input.classList.add('border-gray-300');
                input.classList.remove('border-primary/50');
            });
            formActions.classList.add('hidden');
            editBtn.classList.remove('hidden');
        });
        form.addEventListener('submit', function (e) {
            e.preventDefault();
            // 模拟表单提交
            inputs.forEach(function (input) {
                input.disabled = true;
                input.classList.add('border-gray-300');
                input.classList.remove('border-primary/50');
            });
            formActions.classList.add('hidden');
            editBtn.classList.remove('hidden');

            // 显示成功提示
            var notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-success text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center';
            notification.innerHTML = "<i class=\"fas fa-check-circle mr-2\" data-ytId=\"id-c2qb4\" data-ytId=\"id-siilc\" data-ytId=\"id-839ia\" data-ytId=\"id-ts04h\" data-ytId=\"id-yw6gk\" data-ytId=\"id-mk8ti\" data-ytId=\"id-mudy7\"></i> \u4E2A\u4EBA\u8D44\u6599\u66F4\u65B0\u6210\u529F";
            document.body.appendChild(notification);
            setTimeout(function () {
                notification.classList.add('opacity-0', 'transition-custom');
                setTimeout(function () {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        });
    }
</script>
</body>
</html>
