<template>
  <div class="dynamic-route-test">
    <t-card title="动态路由测试页面">
      <div class="test-section">
        <h3>当前路由信息</h3>
        <t-descriptions :data="routeInfo" />
      </div>

      <div class="test-section">
        <h3>用户信息</h3>
        <t-descriptions :data="userInfo" />
      </div>

      <div class="test-section">
        <h3>权限信息</h3>
        <t-tag v-for="role in userStore.roles" :key="role.id" theme="primary" class="role-tag">
          {{ role.name }}
        </t-tag>
        <t-tag v-for="permission in userStore.permissions" :key="permission" theme="success" class="permission-tag">
          {{ permission }}
        </t-tag>
      </div>

      <div class="test-section">
        <h3>动态路由</h3>
        <t-list :data="dynamicRoutes" />
      </div>

      <div class="test-section">
        <h3>操作测试</h3>
        <t-space>
          <t-button theme="primary" @click="testGenerateRoutes"> 重新生成路由 </t-button>
          <t-button theme="success" @click="testGetUserInfo"> 重新获取用户信息 </t-button>
          <t-button theme="warning" @click="testLogout"> 登出测试 </t-button>
        </t-space>
      </div>
    </t-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore, usePermissionStore } from '@/store';
import { MessagePlugin } from 'tdesign-vue-next';

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const permissionStore = usePermissionStore();

// 路由信息
const routeInfo = computed(() => [
  { label: '路径', value: route.path },
  { label: '名称', value: route.name },
  { label: '参数', value: JSON.stringify(route.params) },
  { label: '查询', value: JSON.stringify(route.query) },
]);

// 用户信息
const userInfo = computed(() => [
  { label: '用户ID', value: userStore.userId },
  { label: '用户名', value: userStore.name },
  { label: '邮箱', value: userStore.email },
  { label: 'Token', value: userStore.token ? '已设置' : '未设置' },
]);

// 动态路由
const dynamicRoutes = computed(() => {
  return permissionStore.menus.map((menu) => ({
    content: `${menu.path} - ${menu.meta?.title || menu.name}`,
  }));
});

// 测试方法
const testGenerateRoutes = async () => {
  try {
    await permissionStore.generateRoutes();
    MessagePlugin.success('路由生成成功');
  } catch (error) {
    MessagePlugin.error('路由生成失败: ' + error.message);
  }
};

const testGetUserInfo = async () => {
  try {
    await userStore.getUserInfo();
    MessagePlugin.success('用户信息获取成功');
  } catch (error) {
    MessagePlugin.error('用户信息获取失败: ' + error.message);
  }
};

const testLogout = async () => {
  try {
    await userStore.logout();
    MessagePlugin.success('登出成功');
    router.push('/login');
  } catch (error) {
    MessagePlugin.error('登出失败: ' + error.message);
  }
};

onMounted(() => {
  console.log('动态路由测试页面已加载');
});
</script>

<style lang="less" scoped>
.dynamic-route-test {
  padding: 24px;
}

.test-section {
  margin-bottom: 24px;

  h3 {
    margin-bottom: 16px;
    color: #1f2937;
  }
}

.role-tag,
.permission-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
