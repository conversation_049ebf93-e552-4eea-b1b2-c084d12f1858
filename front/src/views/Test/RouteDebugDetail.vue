<template>
  <div class="route-debug-detail">
    <t-card title="路由构建详细调试">
      <t-space direction="vertical" size="large">
        
        <!-- 操作按钮 -->
        <t-space>
          <t-button theme="primary" @click="testRouteProcessing">
            <template #icon><RefreshIcon /></template>
            重新测试路由处理
          </t-button>
          <t-button theme="default" @click="clearConsole">
            清空控制台
          </t-button>
        </t-space>

        <!-- 后端原始数据 -->
        <t-card title="1. 后端原始数据" size="small">
          <pre class="json-display">{{ JSON.stringify(backendData, null, 2) }}</pre>
        </t-card>

        <!-- 处理后的路由 -->
        <t-card title="2. 处理后的路由" size="small">
          <pre class="json-display">{{ JSON.stringify(processedRoutes, null, 2) }}</pre>
        </t-card>

        <!-- 已注册的路由 -->
        <t-card title="3. 已注册的路由" size="small">
          <t-table
            :data="registeredRoutes"
            :columns="routeColumns"
            :pagination="false"
            size="small"
          />
        </t-card>

        <!-- 路由分析 -->
        <t-card title="4. 路由分析" size="small">
          <t-descriptions :items="analysisItems" />
        </t-card>

      </t-space>
    </t-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { usePermissionStore } from '@/store'
import { getRouters } from '@/api/auth'
import { RefreshIcon } from 'tdesign-icons-vue-next'

const router = useRouter()
const permissionStore = usePermissionStore()

const backendData = ref([])
const processedRoutes = ref([])
const registeredRoutes = ref([])

// 表格列配置
const routeColumns = [
  { colKey: 'path', title: '路径', width: 200 },
  { colKey: 'name', title: '名称', width: 150 },
  { colKey: 'component', title: '组件', width: 200 },
  { colKey: 'hasChildren', title: '有子路由', width: 100 }
]

// 分析数据
const analysisItems = computed(() => {
  const systemRoutes = registeredRoutes.value.filter(r => 
    r.path.includes('system') || r.name?.includes('system') || r.name?.includes('System')
  )
  
  return [
    { label: '后端数据条数', content: backendData.value.length },
    { label: '处理后路由数', content: processedRoutes.value.length },
    { label: '已注册路由数', content: registeredRoutes.value.length },
    { label: 'System相关路由数', content: systemRoutes.length },
    { label: 'System路由详情', content: systemRoutes.map(r => `${r.path} (${r.name})`).join(', ') }
  ]
})

const refreshRoutes = () => {
  const allRoutes = router.getRoutes()
  registeredRoutes.value = allRoutes.map(r => ({
    path: r.path,
    name: r.name,
    component: r.component?.name || 'unknown',
    hasChildren: !!(r.children && r.children.length > 0)
  }))
}

const testRouteProcessing = async () => {
  try {
    console.log('🚀 开始测试路由处理...')
    
    // 获取后端数据
    const res = await getRouters()
    backendData.value = res.data
    console.log('📡 后端原始数据:', res.data)
    
    // 重新生成路由
    await permissionStore.generateRoutes()
    
    // 获取处理后的路由
    processedRoutes.value = permissionStore.menus
    console.log('🔧 处理后的路由:', processedRoutes.value)
    
    // 刷新已注册路由
    refreshRoutes()
    
    console.log('✅ 路由处理测试完成')
  } catch (error) {
    console.error('❌ 测试路由处理失败:', error)
  }
}

const clearConsole = () => {
  console.clear()
}

onMounted(() => {
  refreshRoutes()
  if (permissionStore.menus.length > 0) {
    processedRoutes.value = permissionStore.menus
  }
})
</script>

<style scoped>
.route-debug-detail {
  padding: 20px;
}

.json-display {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
