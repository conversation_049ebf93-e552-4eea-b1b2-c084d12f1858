<template>
  <div class="route-debug">
    <h2>路由调试页面</h2>
    
    <div class="section">
      <h3>后端路由数据</h3>
      <pre>{{ JSON.stringify(backendData, null, 2) }}</pre>
    </div>
    
    <div class="section">
      <h3>处理后的路由</h3>
      <pre>{{ JSON.stringify(processedRoutes, null, 2) }}</pre>
    </div>
    
    <div class="section">
      <h3>已注册的路由</h3>
      <pre>{{ JSON.stringify(registeredRoutes, null, 2) }}</pre>
    </div>
    
    <div class="section">
      <h3>操作</h3>
      <t-button @click="testRouteProcessing">测试路由处理</t-button>
      <t-button @click="refreshRoutes">刷新路由信息</t-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { usePermissionStore } from '@/store'
import { getRouters } from '@/api/auth'

const router = useRouter()
const permissionStore = usePermissionStore()

const backendData = ref([])
const processedRoutes = ref([])
const registeredRoutes = ref([])

const refreshRoutes = () => {
  const allRoutes = router.getRoutes()
  registeredRoutes.value = allRoutes.map(r => ({
    path: r.path,
    name: r.name,
    component: r.component?.name || 'unknown',
    hasChildren: !!(r.children && r.children.length > 0)
  }))
}

const testRouteProcessing = async () => {
  try {
    // 获取后端数据
    const res = await getRouters()
    backendData.value = res.data
    
    // 处理路由
    processedRoutes.value = permissionStore.menus
    
    // 刷新已注册路由
    refreshRoutes()
  } catch (error) {
    console.error('测试路由处理失败:', error)
  }
}

onMounted(() => {
  refreshRoutes()
  if (permissionStore.menus.length > 0) {
    processedRoutes.value = permissionStore.menus
  }
})
</script>

<style scoped>
.route-debug {
  padding: 20px;
}

.section {
  margin-bottom: 30px;
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 5px;
}

.section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 3px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.t-button {
  margin-right: 10px;
}
</style>
