<template>
  <div class="profile-container">
    <!-- 页面标题横幅 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">个人中心</h1>
        <p class="page-subtitle">管理您的账户信息、偏好设置和安全选项</p>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-grid">
        <!-- 左侧个人信息卡片 -->
        <div class="sidebar">
          <div class="profile-card">
            <!-- 个人头像 -->
            <div class="avatar-section">
              <div class="avatar-wrapper">
                <img
                  :src="userInfo.avatar"
                  alt="用户头像"
                  class="user-avatar"
                />
                <button class="avatar-upload-btn" @click="handleAvatarUpload">
                  <camera-icon :size="16" />
                </button>
              </div>
              <h2 class="user-name">{{ userInfo.name }}</h2>
              <p class="user-email">{{ userInfo.email }}</p>
              <div class="user-status">
                <i class="status-icon">●</i>
                <span>已验证邮箱</span>
              </div>
            </div>

            <!-- 统计信息 -->
            <div class="stats-section">
              <div class="stat-item">
                <div class="stat-number">{{ emailStats.inbox }}</div>
                <div class="stat-label">收件箱</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧内容区域 -->
        <div class="main-panel">
          <!-- 个人资料设置 -->
          <div class="section-card">
            <div class="section-header">
              <h2 class="section-title">个人资料</h2>
              <button
                v-if="!isEditing"
                class="edit-btn"
                @click="startEditing"
              >
                <i class="edit-icon">✏️</i>
                <span>编辑</span>
              </button>
            </div>

            <form class="profile-form" @submit.prevent="handleSave">
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label">姓名</label>
                  <input
                    v-model="userInfo.name"
                    type="text"
                    class="form-input"
                    :disabled="!isEditing"
                  />
                </div>

                <div class="form-group">
                  <label class="form-label">昵称</label>
                  <input
                    v-model="userInfo.nickname"
                    type="text"
                    class="form-input"
                    :disabled="!isEditing"
                  />
                </div>

                <div class="form-group">
                  <label class="form-label">电子邮箱</label>
                  <input
                    v-model="userInfo.email"
                    type="email"
                    class="form-input"
                    :disabled="!isEditing"
                  />
                </div>

                <div class="form-group">
                  <label class="form-label">手机号码</label>
                  <input
                    v-model="userInfo.phone"
                    type="tel"
                    class="form-input"
                    :disabled="!isEditing"
                  />
                </div>

                <div class="form-group form-group-full">
                  <label class="form-label">个人简介</label>
                  <textarea
                    v-model="userInfo.bio"
                    class="form-textarea"
                    rows="3"
                    :disabled="!isEditing"
                  ></textarea>
                </div>
              </div>

              <div v-if="isEditing" class="form-actions">
                <button type="button" class="btn btn-secondary" @click="cancelEditing">
                  取消
                </button>
                <button type="submit" class="btn btn-primary">
                  保存更改
                </button>
              </div>
            </form>
          </div>

          <!-- 已添加邮箱 -->
          <div class="section-card">
            <div class="section-header">
              <h2 class="section-title">已添加邮箱</h2>
              <button class="add-btn" @click="handleAddEmail">
                <i class="add-icon">+</i>
                <span>添加邮箱</span>
              </button>
            </div>

            <div class="email-list">
              <div
                v-for="email in emailList"
                :key="email.id"
                class="email-item"
                :class="{ 'email-item-primary': email.isPrimary }"
              >
                <div class="email-info">
                  <div class="email-icon">
                    <i class="envelope-icon">✉️</i>
                  </div>
                  <div class="email-details">
                    <h3 class="email-address">{{ email.address }}</h3>
                    <p class="email-type">{{ email.type }} · {{ email.status }}</p>
                  </div>
                </div>
                <div class="email-actions">
                  <button class="action-btn" @click="editEmail(email)">
                    <i class="edit-icon">✏️</i>
                  </button>
                  <button class="action-btn" @click="showEmailMenu(email)">
                    <i class="menu-icon">⋮</i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>



<script setup>
import { reactive, ref } from 'vue'
import { CameraIcon } from 'lucide-vue-next'

// 编辑状态
const isEditing = ref(false)

// Mock 用户信息
const userInfo = reactive({
  name: '张明',
  nickname: '明哥',
  email: '<EMAIL>',
  phone: '138****5678',
  bio: '热爱技术的前端开发工程师，喜欢探索新的技术和工具。',
  avatar: 'https://design.gemcoder.com/staticResource/echoAiSystemImages/3acb85c31c2c2642e3d65a2bfa801c11.png'
})

// 邮箱统计
const emailStats = reactive({
  inbox: 128
})

// 邮箱列表
const emailList = reactive([
  {
    id: 1,
    address: '<EMAIL>',
    type: '主邮箱',
    status: '已验证',
    isPrimary: true
  },
  {
    id: 2,
    address: '<EMAIL>',
    type: '工作邮箱',
    status: '已验证',
    isPrimary: false
  },
  {
    id: 3,
    address: '<EMAIL>',
    type: '个人邮箱',
    status: '已验证',
    isPrimary: false
  }
])

// 保存原始数据用于重置
const originalUserInfo = { ...userInfo }

// 开始编辑
const startEditing = () => {
  isEditing.value = true
}

// 取消编辑
const cancelEditing = () => {
  Object.assign(userInfo, originalUserInfo)
  isEditing.value = false
}

// 处理头像上传
const handleAvatarUpload = () => {
  showNotification('头像上传功能开发中...', 'info')
}

// 保存更改
const handleSave = () => {
  isEditing.value = false
  showNotification('个人资料更新成功', 'success')
}

// 添加邮箱
const handleAddEmail = () => {
  showNotification('添加邮箱功能开发中...', 'info')
}

// 编辑邮箱
const editEmail = (email) => {
  showNotification(`编辑邮箱: ${email.address}`, 'info')
}

// 显示邮箱菜单
const showEmailMenu = (email) => {
  showNotification(`邮箱操作菜单: ${email.address}`, 'info')
}

// 显示通知
const showNotification = (message, type = 'info') => {
  const notification = document.createElement('div')
  notification.className = `notification notification-${type}`
  notification.innerHTML = `
    <i class="notification-icon">${type === 'success' ? '✓' : 'ℹ'}</i>
    ${message}
  `
  document.body.appendChild(notification)

  setTimeout(() => {
    notification.classList.add('notification-fade')
    setTimeout(() => {
      document.body.removeChild(notification)
    }, 300)
  }, 3000)
}
</script>

<style scoped>
/* 全局容器 */
.profile-container {
  min-height: 100vh;
  background: #f2f3f5;
  font-family: 'Inter', system-ui, sans-serif;
}

/* 页面标题横幅 */
.page-header {
  background: linear-gradient(135deg, #165DFF 0%, #4080FF 100%);
  border-radius: 16px;
  padding: 32px;
  margin: 32px auto;
  max-width: 1400px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.header-content {
  max-width: 100%;
}

.page-title {
  font-size: clamp(1.75rem, 3vw, 2.5rem);
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  max-width: 512px;
  margin: 0;
  font-size: 1rem;
}

/* 主要内容区域 */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px 32px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 32px;
}

/* 左侧边栏 */
.sidebar {
  display: flex;
  flex-direction: column;
}

.profile-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.profile-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 头像区域 */
.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
}

.avatar-wrapper {
  position: relative;
  margin-bottom: 16px;
}

.user-avatar {
  width: 128px;
  height: 128px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.avatar-upload-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  background: #165DFF;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.avatar-upload-btn:hover {
  background: rgba(22, 93, 255, 0.9);
  transform: scale(1.05);
}

.user-name {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #1D2129;
}

.user-email {
  font-size: 0.875rem;
  color: #86909C;
  margin: 0 0 8px 0;
}

.user-status {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: #00B42A;
}

.status-icon {
  margin-right: 4px;
  font-size: 8px;
}

/* 统计信息 */
.stats-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
  text-align: center;
}

.stat-item {
  padding: 16px;
  background: #f2f3f5;
  border-radius: 8px;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #165DFF;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #86909C;
}

/* 右侧主面板 */
.main-panel {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1D2129;
  margin: 0;
}

.edit-btn, .add-btn {
  color: #165DFF;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.edit-btn:hover, .add-btn:hover {
  color: rgba(22, 93, 255, 0.8);
}

.edit-icon, .add-icon {
  font-size: 0.875rem;
}

/* 表单样式 */
.profile-form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group-full {
  grid-column: span 2;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 4px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
}

.form-input:focus, .form-textarea:focus {
  border-color: rgba(22, 93, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
}

.form-input:disabled, .form-textarea:disabled {
  background-color: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.form-textarea {
  resize: none;
  font-family: inherit;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #f3f4f6;
}

.btn {
  padding: 8px 24px;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
}

.btn-primary {
  background: #165DFF;
  color: white;
}

.btn-primary:hover {
  background: rgba(22, 93, 255, 0.9);
}

.btn-secondary {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #f9fafb;
}

/* 邮箱列表 */
.email-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.email-item:hover {
  border-color: rgba(22, 93, 255, 0.5);
  background: rgba(232, 243, 255, 0.2);
}

.email-item-primary {
  border-color: #165DFF;
  background: rgba(232, 243, 255, 0.03);
}

.email-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.email-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.email-item-primary .email-icon {
  background: rgba(22, 93, 255, 0.1);
  color: #165DFF;
}

.email-item:not(.email-item-primary) .email-icon {
  background: rgba(64, 128, 255, 0.1);
  color: #4080FF;
}

.email-details h3 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #1D2129;
}

.email-details p {
  font-size: 0.875rem;
  color: #86909C;
  margin: 0;
}

.email-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 16px;
  right: 16px;
  background: #00B42A;
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-info {
  background: #165DFF;
}

.notification-fade {
  opacity: 0;
  transform: translateX(100%);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-group-full {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 0 8px 16px;
  }

  .page-header {
    margin: 16px auto;
    padding: 24px 16px;
  }

  .section-card {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .email-info {
    gap: 12px;
  }

  .email-icon {
    width: 32px;
    height: 32px;
    font-size: 0.875rem;
  }
}
</style>
