<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <t-icon name="lock-on" size="120px" />
        </div>
        <h1 class="error-title">403</h1>
        <p class="error-description">抱歉，您没有权限访问此页面</p>
        <div class="error-actions">
          <t-button theme="primary" @click="goHome">
            返回首页
          </t-button>
          <t-button variant="outline" @click="goBack">
            返回上页
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="less" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.error-container {
  text-align: center;
  color: white;
}

.error-icon {
  margin-bottom: 24px;
  opacity: 0.8;
}

.error-title {
  font-size: 72px;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.error-description {
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>
