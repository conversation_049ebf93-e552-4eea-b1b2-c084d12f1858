<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-content">
        <div class="error-icon">
          <t-icon name="error-circle" size="120px" />
        </div>
        <h1 class="error-title">500</h1>
        <p class="error-description">服务器内部错误，请稍后重试</p>
        <div class="error-actions">
          <t-button theme="primary" @click="goHome">
            返回首页
          </t-button>
          <t-button variant="outline" @click="refresh">
            刷新页面
          </t-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const refresh = () => {
  window.location.reload()
}
</script>

<style lang="less" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.error-container {
  text-align: center;
  color: white;
}

.error-icon {
  margin-bottom: 24px;
  opacity: 0.8;
}

.error-title {
  font-size: 72px;
  font-weight: bold;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.error-description {
  font-size: 18px;
  margin-bottom: 32px;
  opacity: 0.9;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}
</style>
