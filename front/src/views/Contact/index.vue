<template>
  <div class="contact-page">
    <div class="container">
      <div class="header-section">
        <h1 class="page-title">联系我们</h1>
        <p class="page-description">
          有任何问题或建议？我们很乐意为您提供帮助
        </p>
      </div>

      <div class="content-section">
        <div class="contact-form">
          <t-card>
            <template #header>
              <h2>发送消息</h2>
            </template>
            
            <t-form
              :data="formData"
              :rules="rules"
              ref="formRef"
              @submit="handleSubmit"
            >
              <t-form-item label="姓名" name="name">
                <t-input
                  v-model="formData.name"
                  placeholder="请输入您的姓名"
                />
              </t-form-item>

              <t-form-item label="邮箱" name="email">
                <t-input
                  v-model="formData.email"
                  placeholder="请输入您的邮箱地址"
                  type="email"
                />
              </t-form-item>

              <t-form-item label="主题" name="subject">
                <t-input
                  v-model="formData.subject"
                  placeholder="请输入消息主题"
                />
              </t-form-item>

              <t-form-item label="消息内容" name="message">
                <t-textarea
                  v-model="formData.message"
                  placeholder="请输入您的消息内容"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                />
              </t-form-item>

              <t-form-item>
                <t-button
                  theme="primary"
                  type="submit"
                  size="large"
                  :loading="loading"
                  block
                >
                  发送消息
                </t-button>
              </t-form-item>
            </t-form>
          </t-card>
        </div>

        <div class="contact-info">
          <h2>联系信息</h2>
          
          <div class="info-item">
            <t-icon name="mail" size="20px" />
            <div>
              <h3>邮箱</h3>
              <p><EMAIL></p>
            </div>
          </div>

          <div class="info-item">
            <t-icon name="call" size="20px" />
            <div>
              <h3>电话</h3>
              <p>+86 400-123-4567</p>
            </div>
          </div>

          <div class="info-item">
            <t-icon name="location" size="20px" />
            <div>
              <h3>地址</h3>
              <p>北京市朝阳区科技园区<br>创新大厦 1001室</p>
            </div>
          </div>

          <div class="info-item">
            <t-icon name="time" size="20px" />
            <div>
              <h3>工作时间</h3>
              <p>周一至周五 9:00-18:00<br>周六 10:00-16:00</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const formRef = ref()
const loading = ref(false)

const formData = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const rules = {
  name: [
    { required: true, message: '请输入您的姓名' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址' },
    { type: 'email', message: '请输入正确的邮箱格式' }
  ],
  subject: [
    { required: true, message: '请输入消息主题' }
  ],
  message: [
    { required: true, message: '请输入消息内容' },
    { min: 10, message: '消息内容至少10个字符' }
  ]
}

const handleSubmit = async ({ validateResult }) => {
  if (validateResult === true) {
    loading.value = true
    try {
      // 模拟发送消息API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 重置表单
      Object.keys(formData).forEach(key => {
        formData[key] = ''
      })
      
      // 显示成功消息
      console.log('消息发送成功')
    } catch (error) {
      console.error('发送失败:', error)
    } finally {
      loading.value = false
    }
  }
}
</script>

<style lang="less" scoped>
.contact-page {
  min-height: calc(100vh - 64px);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 40px 16px;

  @media (min-width: 640px) {
    padding: 60px 24px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 16px;

  @media (min-width: 768px) {
    font-size: 40px;
  }
}

.page-description {
  font-size: 18px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.content-section {
  display: grid;
  grid-template-columns: 1fr;
  gap: 40px;

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
}

.contact-form {
  .t-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
}

.contact-info {
  h2 {
    font-size: 20px;
    font-weight: 600;
    color: #111827;
    margin-bottom: 24px;
  }
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
    margin: 0 0 4px 0;
  }

  p {
    color: #6b7280;
    margin: 0;
    line-height: 1.5;
  }
}
</style>
