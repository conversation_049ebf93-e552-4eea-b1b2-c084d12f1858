import { api } from '@/utils/request'

/**
 * 用户管理相关API
 */

// 获取所有用户（分页）
export const getUserList = (params = {}) => {
  return api.get('/api/users', { params })
}

// 根据ID获取用户详情
export const getUserById = (id) => {
  return api.get(`/api/users/${id}`)
}

// 创建用户
export const createUser = (data) => {
  return api.post('/api/users', {
    username: data.username,
    email: data.email,
    password: data.password,
    mp_open_id: data.mp_open_id || null
  })
}

// 更新用户
export const updateUser = (id, data) => {
  return api.put(`/api/users/${id}`, {
    username: data.username,
    email: data.email,
    mp_open_id: data.mp_open_id || null
  })
}

// 删除用户
export const deleteUser = (id) => {
  return api.delete(`/api/users/${id}`)
}
