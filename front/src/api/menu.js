import { api } from '@/utils/request'

/**
 * 菜单管理相关API
 */

// 获取所有菜单（管理员权限）
export const getMenuList = () => {
  return api.get('/api/menus')
}

// 获取当前用户菜单
export const getUserMenus = () => {
  return api.get('/api/menus/user-menus')
}

// 根据ID获取菜单详情
export const getMenuById = (id) => {
  return api.get(`/api/menus/${id}`)
}

// 创建菜单
export const createMenu = (data) => {
  return api.post('/api/menus', {
    parent_id: data.parent_id,
    name: data.name,
    title: data.title,
    path: data.path,
    component: data.component,
    icon: data.icon,
    sort_order: data.sort_order,
    is_hidden: data.is_hidden,
    is_cache: data.is_cache,
    status: data.status,
    remark: data.remark
  })
}

// 更新菜单
export const updateMenu = (id, data) => {
  return api.put(`/api/menus/${id}`, {
    parent_id: data.parent_id,
    name: data.name,
    title: data.title,
    path: data.path,
    component: data.component,
    icon: data.icon,
    sort_order: data.sort_order,
    is_hidden: data.is_hidden,
    is_cache: data.is_cache,
    status: data.status,
    remark: data.remark
  })
}

// 删除菜单
export const deleteMenu = (id) => {
  return api.delete(`/api/menus/${id}`)
}

// 获取角色的菜单
export const getRoleMenus = (roleId) => {
  return api.get(`/api/menus/role/${roleId}`)
}

// 为角色分配菜单
export const assignMenusToRole = (roleId, menuIds) => {
  return api.post('/api/menus/assign-role', {
    roleId,
    menuIds
  })
}
