import axios from 'axios'
import { api } from '@/utils/request'

/**
 * 域名管理相关API
 */

// 获取所有域名（分页）
export const getDomainList = (params = {}) => {
  return api.get('/api/domains', { params })
}

// 根据ID获取域名详情
export const getDomainById = (id) => {
  return api.get(`/api/domains/${id}`)
}

// 创建域名
export const createDomain = (data) => {
  return api.post('/api/domains', {
    domain_name: data.domain_name,
    status: data.status,
    description: data.description || ''
  })
}

// 更新域名
export const updateDomain = (id, data) => {
  return api.put(`/api/domains/${id}`, {
    domain_name: data.domain_name,
    status: data.status,
    description: data.description || ''
  })
}

// 删除域名
export const deleteDomain = (id) => {
  return api.delete(`/api/domains/${id}`)
}

// 批量删除域名
export const batchDeleteDomains = (ids) => {
  // 使用axios直接发送DELETE请求，并在请求体中传递数据
  const baseURL = import.meta.env.VITE_API_BASE_URL || '/dev-api'
  return axios({
    method: 'delete',
    url: `${baseURL}/api/domains`,
    data: { ids },
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
    }
  })
}

// 更新域名状态
export const updateDomainStatus = (id, status) => {
  return api.patch(`/api/domains/${id}/status`, {
    status
  })
}

// 获取启用的域名列表
export const getEnabledDomains = () => {
  return api.get('/api/domains/enabled/list')
}

// 获取启用的域名列表（公开接口，无需认证）
export const getPublicEnabledDomains = () => {
  return api.get('/api/domains/public/enabled')
}
