# API 接口封装说明

## 文件结构
```
src/
├── api/
│   ├── index.js          # 统一导出
│   ├── auth.js           # 基础认证API
│   └── README.md         # 说明文档
└── utils/
    ├── validate.js       # 表单验证工具
    └── auth.js           # 认证操作工具
```

## 认证相关接口 (api/auth.js)

### 基础API方法

#### 1. 用户注册
```javascript
import { register } from '@/api'

const result = await register({
  username: '用户名',
  email: '<EMAIL>',
  password: '密码',
  mp_open_id: '微信openid' // 可选
})
```

#### 2. 用户登录
```javascript
import { login } from '@/api'

const result = await login({
  email: '<EMAIL>',
  password: '密码'
})
```

#### 3. 获取用户信息
```javascript
import { getUserProfile } from '@/api'

const userInfo = await getUserProfile()
```

#### 4. 修改密码
```javascript
import { changePassword } from '@/api'

const result = await changePassword({
  oldPassword: '旧密码',
  newPassword: '新密码'
})
```

#### 5. 用户登出
```javascript
import { logout } from '@/api'

const result = await logout()
```

## 认证操作工具 (utils/auth.js)

#### 1. 执行登录（自动保存到store）
```javascript
import { performLogin } from '@/api'

const result = await performLogin({
  email: '<EMAIL>',
  password: '密码'
})

if (result.success) {
  console.log('登录成功:', result.message)
  // 用户信息已自动保存到userStore
} else {
  console.error('登录失败:', result.message)
}
```

#### 2. 执行注册（自动登录）
```javascript
import { performRegister } from '@/api'

const result = await performRegister({
  username: '用户名',
  email: '<EMAIL>',
  password: '密码'
})

if (result.success) {
  console.log('注册成功:', result.message)
  // 已自动登录并保存用户信息
} else {
  console.error('注册失败:', result.message)
}
```

#### 3. 执行登出（自动清除store）
```javascript
import { performLogout } from '@/api'

const result = await performLogout()
console.log(result.message) // 登出成功
```

#### 4. 其他认证工具
```javascript
import {
  checkAuthStatus,
  getCurrentUser,
  getAuthHeader,
  forceLogout,
  autoLoginCheck
} from '@/api'

// 检查登录状态
const isLoggedIn = checkAuthStatus()

// 获取当前用户
const user = getCurrentUser()

// 获取认证头
const authHeader = getAuthHeader()

// 强制登出
forceLogout()

// 自动登录检查
const autoLogin = autoLoginCheck()
```

## 表单验证工具 (utils/validate.js)

#### 1. 基础验证函数
```javascript
import {
  validateEmail,
  validatePassword,
  validateUsername,
  validatePhone,
  validateRequired
} from '@/api'

const isValidEmail = validateEmail('<EMAIL>')
const isValidPassword = validatePassword('abc123')
const isValidUsername = validateUsername('用户名123')
const isValidPhone = validatePhone('13800138000')
const isRequired = validateRequired('value')
```

#### 2. 组合验证函数
```javascript
import {
  validateLoginForm,
  validateRegisterForm,
  validateChangePasswordForm
} from '@/api'

// 验证登录表单
const loginResult = validateLoginForm({
  email: '<EMAIL>',
  password: 'password123'
})

// 验证注册表单
const registerResult = validateRegisterForm({
  username: '用户名',
  email: '<EMAIL>',
  password: 'password123',
  confirmPassword: 'password123'
})

// 验证修改密码表单
const changePasswordResult = validateChangePasswordForm({
  oldPassword: 'oldpass',
  newPassword: 'newpass123',
  confirmPassword: 'newpass123'
})

if (loginResult.isValid) {
  console.log('表单验证通过')
} else {
  console.log('验证错误:', loginResult.errors)
}
```

## 在组件中的使用示例

### 登录组件
```vue
<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { performLogin, validateEmail } from '@/api'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()
const loading = ref(false)
const formData = ref({
  email: '',
  password: ''
})

const handleLogin = async () => {
  // 表单验证
  if (!validateEmail(formData.value.email)) {
    MessagePlugin.error('请输入正确的邮箱格式')
    return
  }
  
  if (!formData.value.password) {
    MessagePlugin.error('请输入密码')
    return
  }
  
  loading.value = true
  
  try {
    const result = await performLogin(formData.value)
    
    if (result.success) {
      MessagePlugin.success(result.message)
      router.push('/my-mailbox')
    } else {
      MessagePlugin.error(result.message)
    }
  } catch (error) {
    MessagePlugin.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>
```

### 注册组件
```vue
<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { performRegister, validateEmail, validatePassword, validateUsername } from '@/api'
import { MessagePlugin } from 'tdesign-vue-next'

const router = useRouter()
const loading = ref(false)
const formData = ref({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
})

const handleRegister = async () => {
  // 表单验证
  if (!validateUsername(formData.value.username)) {
    MessagePlugin.error('用户名格式不正确（2-20位，中英文数字下划线）')
    return
  }
  
  if (!validateEmail(formData.value.email)) {
    MessagePlugin.error('请输入正确的邮箱格式')
    return
  }
  
  if (!validatePassword(formData.value.password)) {
    MessagePlugin.error('密码至少6位，需包含字母和数字')
    return
  }
  
  if (formData.value.password !== formData.value.confirmPassword) {
    MessagePlugin.error('两次输入的密码不一致')
    return
  }
  
  loading.value = true
  
  try {
    const result = await performRegister({
      username: formData.value.username,
      email: formData.value.email,
      password: formData.value.password
    })
    
    if (result.success) {
      MessagePlugin.success(result.message)
      router.push('/my-mailbox')
    } else {
      MessagePlugin.error(result.message)
    }
  } catch (error) {
    MessagePlugin.error('注册失败，请稍后重试')
  } finally {
    loading.value = false
  }
}
</script>
```

## 后端接口对应关系

| 前端方法 | 后端接口 | 说明 |
|---------|---------|------|
| register() | POST /api/auth/register | 用户注册 |
| login() | POST /api/auth/login | 用户登录 |
| getUserProfile() | GET /api/auth/profile | 获取用户信息 |
| changePassword() | PUT /api/auth/password | 修改密码 |
| logout() | POST /api/auth/logout | 用户登出 |

## 响应数据格式

所有接口都遵循统一的响应格式：
```javascript
{
  code: 200,        // 状态码
  data: {},         // 响应数据
  message: "成功"   // 响应消息
}
```
