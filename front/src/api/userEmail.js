import { api } from '@/utils/request'

/**
 * 用户邮箱关联相关API
 */

// 添加用户邮箱关联
export const addUserEmail = (emailAddress) => {
  return api.post('/api/user-emails', {
    email_address: emailAddress
  })
}

// 获取用户邮箱列表
export const getUserEmailList = (params = {}) => {
  return api.get('/api/user-emails', { params })
}

// 根据ID获取用户邮箱详情
export const getUserEmailById = (id) => {
  return api.get(`/api/user-emails/${id}`)
}

// 删除用户邮箱关联
export const deleteUserEmail = (id) => {
  return api.delete(`/api/user-emails/${id}`)
}

// 根据邮箱地址删除用户邮箱关联
export const deleteUserEmailByAddress = (emailAddress) => {
  return api.delete(`/api/user-emails/email/${encodeURIComponent(emailAddress)}`)
}

// 获取用户关联邮箱的邮件列表
export const getUserEmails = (params = {}) => {
  return api.get('/api/user-emails/emails', { params })
}
