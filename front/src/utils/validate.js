/**
 * 验证工具函数
 */

/**
 * 检查是否为外部链接
 * @param {string} url
 * @returns {boolean}
 */
export function isHttp(url) {
  if (!url || typeof url !== 'string') return false
  return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1
}

/**
 * 检查路径是否匹配模式
 * @param {string} pattern 模式字符串，支持通配符 *
 * @param {string} path 要检查的路径
 * @returns {boolean}
 */
export function isPathMatch(pattern, path) {
  if (pattern === path) return true

  // 处理通配符 *
  if (pattern.includes('*')) {
    const regexPattern = pattern.replace(/\*/g, '.*')
    const regex = new RegExp(`^${regexPattern}$`)
    return regex.test(path)
  }

  return false
}

/**
 * 验证邮箱格式
 * @param {string} email
 * @returns {boolean}
 */
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证密码强度
 * @param {string} password
 * @returns {boolean}
 */
export function validatePassword(password) {
  // 至少6位，包含字母和数字
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/
  return passwordRegex.test(password)
}

/**
 * 验证用户名
 * @param {string} username
 * @returns {boolean}
 */
export function validateUsername(username) {
  // 2-20位，支持中文、英文、数字、下划线
  const usernameRegex = /^[\u4e00-\u9fa5a-zA-Z0-9_]{2,20}$/
  return usernameRegex.test(username)
}

/**
 * 验证必填字段
 * @param {any} value
 * @returns {boolean}
 */
export function validateRequired(value) {
  if (value === null || value === undefined) return false
  if (typeof value === 'string') return value.trim().length > 0
  if (Array.isArray(value)) return value.length > 0
  return true
}
