/**
 * 模拟后端路由数据
 * 用于测试动态路由功能
 */

export const mockRoutes = [
  {
    path: '',
    component: 'Layout',
    redirect: '/get-mailbox',
    children: [
      {
        path: '/get-mailbox',
        component: 'GetMailbox/index',
        name: 'GetMailbox',
        meta: { 
          title: '获取邮箱', 
          icon: 'mail',
          affix: false
        }
      }
    ]
  },
  {
    path: '',
    component: 'Layout', 
    redirect: '/my-mailbox',
    children: [
      {
        path: '/my-mailbox',
        component: 'MyMailbox/index',
        name: 'MyMailbox',
        meta: { 
          title: '我的邮箱', 
          icon: 'inbox',
          affix: false
        }
      }
    ]
  }
]

/**
 * 模拟getRouters API响应
 */
export const mockGetRoutersResponse = {
  code: 200,
  data: mockRoutes,
  message: '获取用户菜单成功'
}
