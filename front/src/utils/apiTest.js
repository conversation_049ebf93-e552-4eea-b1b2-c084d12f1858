/**
 * API 接口测试工具
 * 用于测试后端接口连接是否正常
 */
import { api } from '@/utils/request'
import { login, register } from '@/api/auth'

// 测试基础连接
export const testConnection = async () => {
  try {
    console.log('🔍 测试后端连接...')
    const response = await api.get('/api/health')
    console.log('✅ 后端连接成功:', response)
    return true
  } catch (error) {
    console.error('❌ 后端连接失败:', error)
    return false
  }
}

// 测试注册接口
export const testRegister = async () => {
  try {
    console.log('🔍 测试注册接口...')
    const testData = {
      username: 'test_user_' + Date.now(),
      email: 'test_' + Date.now() + '@example.com',
      password: 'test123456'
    }

    const response = await register(testData)
    console.log('✅ 注册接口测试成功:', response)
    return response
  } catch (error) {
    console.error('❌ 注册接口测试失败:', error)
    return null
  }
}

// 测试登录接口
export const testLogin = async (email = '<EMAIL>', password = 'test123456') => {
  try {
    console.log('🔍 测试登录接口...')
    const response = await login({ email, password })
    console.log('✅ 登录接口测试成功:', response)
    return response
  } catch (error) {
    console.error('❌ 登录接口测试失败:', error)
    return null
  }
}

// 运行所有测试
export const runAllTests = async () => {
  console.log('🚀 开始 API 接口测试...')

  // 测试连接
  const connectionOk = await testConnection()
  if (!connectionOk) {
    console.log('❌ 基础连接失败，停止测试')
    return
  }

  // 测试注册
  const registerResult = await testRegister()

  // 如果注册成功，测试登录
  if (registerResult && registerResult.code === 200) {
    const email = registerResult.data.user.email
    await testLogin(email, 'test123456')
  }

  console.log('🏁 API 接口测试完成')
}

// 在浏览器控制台中使用的快捷方法
if (typeof window !== 'undefined') {
  window.apiTest = {
    testConnection,
    testRegister,
    testLogin,
    runAllTests
  }
  console.log('💡 API 测试工具已加载，可在控制台使用：')
  console.log('   - apiTest.testConnection()')
  console.log('   - apiTest.testRegister()')
  console.log('   - apiTest.testLogin()')
  console.log('   - apiTest.runAllTests()')
}
