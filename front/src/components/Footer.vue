<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <!-- Logo Section -->
        <div class="footer-section">
          <div class="footer-logo">
            <t-icon name="mail" size="24px" />
            <span class="footer-logo-text">MailCode</span>
          </div>
          <p class="footer-description">下一代无限邮箱系统，让邮件管理变得简单高效。</p>
        </div>

        <!-- Product Section -->
        <div class="footer-section">
          <h3 class="footer-title">产品</h3>
          <ul class="footer-links">
            <li><a href="#" class="footer-link">功能特性</a></li>
            <li><a href="#" class="footer-link">价格方案</a></li>
            <li><a href="#" class="footer-link">企业版</a></li>
            <li><a href="#" class="footer-link">API文档</a></li>
          </ul>
        </div>

        <!-- Support Section -->
        <div class="footer-section">
          <h3 class="footer-title">支持</h3>
          <ul class="footer-links">
            <li><a href="#" class="footer-link">帮助中心</a></li>
            <li><a href="#" class="footer-link">联系我们</a></li>
            <li><a href="#" class="footer-link">状态页面</a></li>
            <li><a href="#" class="footer-link">社区论坛</a></li>
          </ul>
        </div>

        <!-- Company Section -->
        <div class="footer-section">
          <h3 class="footer-title">公司</h3>
          <ul class="footer-links">
            <li><a href="#" class="footer-link">关于我们</a></li>
            <li><a href="#" class="footer-link">招聘信息</a></li>
            <li><a href="#" class="footer-link">隐私政策</a></li>
            <li><a href="#" class="footer-link">服务条款</a></li>
          </ul>
        </div>
      </div>

      <!-- Copyright -->
      <div class="footer-bottom">
        <p class="copyright">&copy; 2024 MailCode. 保留所有权利。</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
// Footer组件不需要响应式数据或方法
</script>

<style lang="less" scoped>
.footer {
  background: #111827;
  color: white;
  padding: 48px 16px;

  @media (min-width: 640px) {
    padding: 48px 24px;
  }

  @media (min-width: 1024px) {
    padding: 48px 32px;
  }
}

.footer-container {
  max-width: 1280px;
  margin: 0 auto;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;

  @media (min-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.footer-section {
  &:first-child {
    @media (min-width: 768px) {
      grid-column: span 1;
    }
  }
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;

  .footer-logo-text {
    font-size: 18px;
    font-weight: bold;
  }
}

.footer-description {
  color: #9ca3af;
  line-height: 1.6;
}

.footer-title {
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 16px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 8px;
}

.footer-link {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.2s;

  &:hover {
    color: white;
  }
}

.footer-bottom {
  border-top: 1px solid #374151;
  margin-top: 32px;
  padding-top: 32px;
  text-align: center;
}

.copyright {
  color: #9ca3af;
  margin: 0;
}
</style>
