<template>
  <div v-if="visible" class="page-loading-overlay">
    <div class="loading-container">
      <!-- 主要加载动画 -->
      <div class="loading-spinner">
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
        <div class="spinner-ring"></div>
      </div>

      <!-- 加载文字 -->
      <div class="loading-text">
        <span class="text-content">{{ text }}</span>
        <div class="dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </div>

      <!-- 装饰性元素 -->
      <div class="decoration-particles">
        <div class="particle" v-for="i in 6" :key="i"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  text: {
    type: String,
    default: '页面加载中'
  }
})
</script>

<style scoped>
.page-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(10px);
}

.loading-container {
  text-align: center;
  position: relative;
}

/* 主要旋转动画 */
.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 30px;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-radius: 50%;
  animation: spin 2s linear infinite;
}

.spinner-ring:nth-child(1) {
  border-top-color: #4f46e5;
  animation-duration: 1.5s;
}

.spinner-ring:nth-child(2) {
  border-right-color: #7c3aed;
  animation-duration: 2s;
  animation-direction: reverse;
  width: 60px;
  height: 60px;
  top: 10px;
  left: 10px;
}

.spinner-ring:nth-child(3) {
  border-bottom-color: #06b6d4;
  animation-duration: 2.5s;
  width: 40px;
  height: 40px;
  top: 20px;
  left: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载文字动画 */
.loading-text {
  color: #374151;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.text-content {
  opacity: 0;
  animation: fadeInOut 2s ease-in-out infinite;
}

.dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background: #4f46e5;
  border-radius: 50%;
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes fadeInOut {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 装饰性粒子动画 */
.decoration-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(79, 70, 229, 0.6);
  border-radius: 50%;
  animation: float 3s ease-in-out infinite;
}

.particle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.particle:nth-child(2) {
  top: 20%;
  right: 20%;
  animation-delay: 0.5s;
}

.particle:nth-child(3) {
  bottom: 20%;
  left: 20%;
  animation-delay: 1s;
}

.particle:nth-child(4) {
  bottom: 20%;
  right: 20%;
  animation-delay: 1.5s;
}

.particle:nth-child(5) {
  top: 50%;
  left: 10%;
  animation-delay: 2s;
}

.particle:nth-child(6) {
  top: 50%;
  right: 10%;
  animation-delay: 2.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-spinner {
    width: 60px;
    height: 60px;
  }

  .loading-text {
    font-size: 14px;
  }

  .decoration-particles {
    width: 150px;
    height: 150px;
  }
}
</style>
