import { defineStore } from 'pinia'
import { ref } from 'vue'
import cloneDeep from 'lodash/cloneDeep'
import { getRouters } from '@/api/auth'
import Layout from '@/layouts/index.vue'
import router, { defaultRouterList } from '@/router'
import { isHttp } from '@/utils/validate'

// 自动导入views文件夹下所有vue文件
const modules = import.meta.glob('../../views/**/*.vue')

export const usePermissionStore = defineStore('permission', () => {
  const whiteListRouters = ref([
    '/login',
    '/register',
    '/contact',
    '/403',
    '/500',
    '/404',
    '/test',
  ])
  const menus = ref([])
  const allMenus = ref([])

  async function generateRoutes() {
    try {
      console.log('🚀 开始生成动态路由...')
      console.log('📊 当前menus状态:', menus.value.length > 0 ? '已有数据' : '空')

      // 如果已经有菜单数据，直接返回，避免重复生成
      if (menus.value.length > 0) {
        console.log('⚠️ 路由已存在，跳过重复生成')
        return
      }

      // 向后端请求路由数据
      const res = await getRouters()
      console.log('📡 getRouters响应:', res.data)
      console.log('📊 后端数据详细分析:', {
        总数: res.data.length,
        菜单项: res.data.map(item => ({
          id: item.id,
          name: item.name,
          title: item.title,
          component: item.component,
          path: item.path,
          parent_id: item.parent_id,
          有子菜单: !!(item.children && item.children.length > 0),
          子菜单数量: item.children?.length || 0
        }))
      })

      const asyncRouter = filterAsyncRouter(cloneDeep(res.data))
      console.log('🔧 过滤后的路由:', asyncRouter)
      console.log('🔧 过滤后路由详细分析:', {
        总数: asyncRouter.length,
        路由项: asyncRouter.map(route => ({
          path: route.path,
          name: route.name,
          component: route.component?.name || route.component,
          有子路由: !!(route.children && route.children.length > 0),
          子路由数量: route.children?.length || 0
        }))
      })

      menus.value = asyncRouter
      allMenus.value = defaultRouterList.concat(asyncRouter)

      // 清除之前的动态路由（防止重复添加）
      console.log('🧹 清除之前的动态路由...')

      // 获取当前所有路由，找出需要清除的动态路由
      const currentRoutes = router.getRoutes()
      const dynamicRouteNames = []

      currentRoutes.forEach(route => {
        // 清除之前添加的动态路由（非默认路由）
        if (route.name && !['Login', 'Register', 'Contact', 'Index', 'GetMailbox', 'MyMailbox'].includes(route.name)) {
          dynamicRouteNames.push(route.name)
        }
      })

      console.log('🗑️ 准备清除的动态路由:', dynamicRouteNames)

      // 清除动态路由
      dynamicRouteNames.forEach(name => {
        if (router.hasRoute(name)) {
          router.removeRoute(name)
          console.log(`🗑️ 已清除路由: ${name}`)
        }
      })

      // 根据后台路由数据生成可访问路由表
      console.log('🚀 开始添加动态路由到Vue Router...')
      asyncRouter.forEach((route, index) => {
        // Layout路由的path可以是空字符串，这是正常的
        if (route.path !== undefined && !isHttp(route.path)) {
          console.log(`➕ 添加动态路由 [${index}]:`, {
            path: route.path,
            name: route.name,
            component: route.component?.name || 'unknown',
            hasChildren: !!(route.children && route.children.length > 0),
            childrenCount: route.children?.length || 0,
            children: route.children?.map(child => ({
              path: child.path,
              name: child.name,
              component: child.component?.name || 'unknown'
            })) || []
          })
          router.addRoute(route) // 动态添加可访问路由表
        } else {
          console.log(`⚠️ 跳过路由 [${index}] (path未定义或为HTTP链接):`, {
            path: route.path,
            name: route.name,
            component: route.component?.name || 'unknown'
          })
        }
      })

      // 检查最终注册的路由
      console.log('📋 当前所有已注册路由:')
      router.getRoutes().forEach((route, index) => {
        if (route.path.includes('system') || route.name?.toLowerCase().includes('system')) {
          console.log(`🔍 System相关路由 [${index}]:`, {
            path: route.path,
            name: route.name,
            component: route.component?.name || 'unknown'
          })
        }
      })

      // 打印所有已注册的路由
      const allRoutes = router.getRoutes()
      console.log('📋 所有已注册路由:', allRoutes.map(r => ({
        path: r.path,
        name: r.name,
        component: r.component?.name || 'unknown'
      })))

      console.log('✅ 动态路由生成完成')
    } catch (error) {
      console.error('❌ 生成路由失败:', error)
      throw error
    }
  }

  // 检查路径是否在白名单中
  const isWhiteList = (path) => {
    return whiteListRouters.value.some((pattern) => {
      if (pattern === path) return true
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '.*')
        const regex = new RegExp(`^${regexPattern}$`)
        return regex.test(path)
      }
      return false
    })
  }

  // 清空路由
  const clearRoutes = () => {
    menus.value = []
    allMenus.value = [...defaultRouterList]
  }

  // 获取菜单树
  const getMenuTree = () => {
    return menus.value
  }

  return {
    menus,
    allMenus,
    whiteListRouters,
    generateRoutes,
    isWhiteList,
    clearRoutes,
    getMenuTree,
  }
})

/**
 * 遍历后台传来的路由字符串，转换为组件对象
 * @param routers 后台传来的路由字符串
 */
function filterAsyncRouter(routers) {
  console.log('🔧 filterAsyncRouter 输入:', routers.map(r => ({
    title: r.title,
    path: r.path,
    component: r.component,
    hasChildren: !!(r.children && r.children.length > 0),
    parent_id: r.parent_id
  })))

  // 检查是否已经是树形结构（有父子关系）
  const hasTreeStructure = routers.some(r => r.children && r.children.length > 0)
  const hasLayoutComponent = routers.some(r => {
    const componentStr = r.component?.toString() || r.component
    return componentStr === 'Layout'
  })

  console.log('🔍 结构检查:', {
    hasTreeStructure,
    hasLayoutComponent,
    routersCount: routers.length,
    components: routers.map(r => r.component)
  })

  // 新的路由处理逻辑：将所有需要Layout的路由包装在Layout中
  console.log('🏗️ 开始处理路由，将需要Layout的路由包装')

  // 分离Layout路由和普通路由
  const layoutRoutes = []
  const normalRoutes = []

  routers.forEach(route => {
    const componentStr = route.component?.toString() || route.component
    if (componentStr === 'Layout') {
      layoutRoutes.push(route)
    } else {
      normalRoutes.push(route)
    }
  })

  console.log('📊 路由分类:', {
    Layout路由数量: layoutRoutes.length,
    普通路由数量: normalRoutes.length,
    Layout路由: layoutRoutes.map(r => r.title),
    普通路由: normalRoutes.map(r => r.title)
  })

  // 如果有Layout路由，按原逻辑处理
  if (layoutRoutes.length > 0) {
    console.log('🏗️ 处理Layout路由')
    const processedLayoutRoutes = layoutRoutes.map((route) => {
      console.log(`🔧 处理Layout路由: ${route.title}`)
      const processedRoute = processMenuRoute(route)

      // 处理子路由
      if (processedRoute.children?.length) {
        console.log(`📁 处理Layout子路由: ${processedRoute.title} 有 ${processedRoute.children.length} 个子路由`)
        processedRoute.children = processedRoute.children.map(childRoute => {
          console.log(`🔧 处理Layout子路由项: ${childRoute.title} (${childRoute.component})`)
          return processMenuRoute(childRoute)
        })
      }

      return processedRoute
    })

    // 如果还有普通路由，也需要包装在Layout中
    if (normalRoutes.length > 0) {
      console.log('📦 将普通路由包装在Layout中')
      const mainLayoutRoute = {
        path: '',
        name: 'MainLayout',
        component: Layout,
        redirect: normalRoutes[0] ? normalRoutes[0].path : '/index',
        children: []
      }

      // 处理普通路由并添加到Layout的children中
      normalRoutes.forEach(route => {
        const processedRoute = processMenuRoute(route)
        if (processedRoute) {
          mainLayoutRoute.children.push(processedRoute)
        }
      })

      return [mainLayoutRoute, ...processedLayoutRoutes]
    }

    return processedLayoutRoutes
  } else {
    // 如果没有Layout路由，将所有路由包装在Layout中
    console.log('📦 没有Layout路由，创建Layout包装器包装所有路由')
    const layoutRoute = {
      path: '',
      name: 'MainLayout',
      component: Layout,
      redirect: routers[0] ? routers[0].path : '/index',
      children: []
    }

    // 处理每个菜单项
    routers.forEach((route) => {
      const processedRoute = processMenuRoute(route)
      if (processedRoute) {
        layoutRoute.children.push(processedRoute)
      }
    })

    return [layoutRoute]
  }
}

/**
 * 处理单个菜单路由
 */
function processMenuRoute(route) {
  console.log(`🔧 processMenuRoute 开始处理: ${route.title}`, {
    originalPath: route.path,
    component: route.component,
    hasChildren: !!(route.children && route.children.length > 0)
  })

  // 处理路径：优先使用数据库中的path字段，否则根据component生成
  if (route.path) {
    // 对于Layout组件（父级菜单），确保path以斜杠开头
    if (route.component?.toString() === 'Layout') {
      if (!route.path.startsWith('/')) {
        route.path = '/' + route.path
      }
      console.log(`📍 Layout路由path: ${route.title} -> ${route.path}`)
    } else {
      // 对于子级路由，如果有父级，则保持相对路径；否则确保以斜杠开头
      // 这里我们需要检查是否是子路由（通过parent_id或其他方式）
      // 暂时保持原有逻辑，确保非Layout路由的path处理
      console.log(`📍 子级路由path: ${route.title} -> ${route.path} (保持原样)`)
    }
  } else if (route.component) {
    // 根据component生成path
    route.path = componentToPath(route.component)
    console.log(`🔧 根据component生成path: ${route.title} -> ${route.path}`)
  }

  // 构建meta对象
  route.meta = {
    title: route.title,
    icon: route.icon,
    hidden: route.is_hidden === 1,
    cache: route.is_cache === 1
  }

  if (route.component) {
    // Layout组件特殊处理 - 更严格的判断
    const componentStr = route.component?.toString() || route.component
    if (componentStr === 'Layout') {
      route.component = Layout
      // Layout路由通常不需要name，但为了调试方便可以设置
      // 如果有多个Layout路由，可以基于path生成唯一name
      if (route.path && route.path !== '') {
        route.name = route.path.replace(/\//g, '') || 'layout'
        console.log(`🏷️ Layout路由设置name: ${route.title} -> ${route.name}`)
      } else {
        console.log(`🏷️ Layout路由无path，不设置name: ${route.title}`)
      }
    } else {
      // 设置name（用于路由缓存）- 基于component字段生成小写开头的驼峰形式
      // 例如: Index/index -> index, GetMailbox/index -> getMailbox
      let componentName = route.component.replace(/\/index$/, '') // 移除/index后缀
      componentName = componentName.replace(/\//g, '') // 移除斜杠
      // 将首字母转换为小写
      route.name = componentName.charAt(0).toLowerCase() + componentName.slice(1)
      console.log(`🏷️ 普通路由设置name: ${route.title} -> ${route.name}`)

      route.component = loadView(route.component)
    }
  }

  console.log(`✅ processMenuRoute 完成: ${route.title}`, {
    finalPath: route.path,
    finalName: route.name,
    componentType: route.component?.name || 'unknown'
  })

  return route
}

/**
 * 根据组件路径生成路由路径
 * @param component 组件路径，如 'Home/index' 或 'GetMailbox/index'
 */
function componentToPath(component) {
  if (!component) return '/'

  // 移除 /index 后缀
  let cleanComponent = component.replace(/\/index$/, '')

  // 直接使用组件路径作为URL路径
  const path = '/' + cleanComponent

  return path
}

/**
 * 加载组件
 * @param view 组件名称，如 'Home/index'
 */
export const loadView = (view) => {
  console.log(`🔍 尝试加载组件: ${view}`)

  // 构建完整的路径
  const fullPath = `../../views/${view}.vue`
  console.log(`🔍 尝试路径1: ${fullPath}`)

  // 查找匹配的模块
  for (const path in modules) {
    if (path === fullPath) {
      console.log(`✅ 找到组件: ${view} -> ${path}`)
      return () => modules[path]()
    }
  }

  // 如果没找到，尝试其他可能的路径格式
  for (const path in modules) {
    const dir = path.split('views/')[1]?.split('.vue')[0]
    if (dir === view) {
      console.log(`✅ 找到组件(备用路径): ${view} -> ${path}`)
      return () => modules[path]()
    }
  }

  // 打印所有可用的模块路径用于调试
  console.log('📋 所有可用的组件模块:')
  Object.keys(modules).forEach(path => {
    const dir = path.split('views/')[1]?.split('.vue')[0]
    console.log(`  - ${path} -> ${dir}`)
  })

  console.error(`❌ 无法找到组件: ${view}`)
  return null
}


