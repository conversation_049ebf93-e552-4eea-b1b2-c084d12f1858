import { createApp } from 'vue';
import TDesign from 'tdesign-vue-next';

import App from './App.vue';
import router from './router';
import store from './store';

// 引入路由权限控制
import './permission';

import '@/assets/main.css';
import '@/style/index.less';

// 引入组件库全局样式资源
import 'tdesign-vue-next/es/style/index.css';

// 开发环境下加载 API 测试工具
if (import.meta.env.DEV) {
  import('@/utils/apiTest')
}

createApp(App).use(store).use(router).use(TDesign).mount('#app');
