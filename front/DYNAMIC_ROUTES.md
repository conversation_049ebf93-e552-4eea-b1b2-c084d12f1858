# MailCode 动态路由系统

## 🎯 项目概述

MailCode项目已成功实现完整的动态路由系统，完全参考tellyou项目的设计模式，提供基于用户权限的动态路由生成和菜单管理功能。

## 📁 核心文件结构

```
front/src/
├── router/
│   ├── index.js              # 路由配置（参考tellyou/index.ts）
│   └── README.md             # 路由系统说明
├── store/modules/
│   ├── permission.js         # 权限管理（参考tellyou/permission1.ts）
│   └── user.js              # 用户管理（参考tellyou/user.ts）
├── permission.js             # 路由守卫（参考tellyou/permission.ts）
├── layouts/
│   └── index.vue            # 布局组件
├── utils/
│   ├── validate.js          # 验证工具
│   └── mockRoutes.js        # 测试数据
└── views/
    ├── 404/index.vue        # 404错误页
    ├── 403/index.vue        # 403权限错误页
    ├── 500/index.vue        # 500服务器错误页
    └── Test/DynamicRoute.vue # 动态路由测试页
```

## 🔧 核心功能

### 1. 路由管理 (`router/index.js`)
- ✅ **固定路由**: 登录、注册、联系我们、错误页面
- ✅ **动态路由**: 从后端获取的权限路由
- ✅ **Layout包装**: 统一页面布局
- ✅ **路由工具**: 展开路由、激活路径获取

### 2. 权限控制 (`store/modules/permission.js`)
- ✅ **动态路由生成**: `generateRoutes()`
- ✅ **路由格式转换**: `filterAsyncRouter()`
- ✅ **组件动态加载**: `loadView()`
- ✅ **白名单管理**: 支持通配符匹配

### 3. 路由守卫 (`permission.js`)
- ✅ **前置守卫**: 权限验证、路由生成
- ✅ **后置守卫**: 登录检查、标题设置
- ✅ **错误重试**: 防止无限循环
- ✅ **白名单检查**: 自动放行公开页面

## 🚀 API接口

### 后端接口要求
```javascript
// 1. 获取用户路由菜单
GET /api/auth/getRouters
Response: {
  code: 200,
  data: [
    {
      path: '/get-mailbox',
      name: 'GetMailbox',
      component: 'GetMailbox/index',
      meta: { title: '获取邮箱', icon: 'mail' }
    }
  ],
  message: '获取用户菜单成功'
}

// 2. 获取用户基本信息
GET /api/auth/getInfo
Response: {
  code: 200,
  data: {
    user: { id, username, email, ... },
    roles: [{ id, name, code }],
    permissions: ['user:query', 'mail:create']
  },
  message: '获取用户信息成功'
}

// 3. 检查登录状态
GET /api/auth/isLogin
Response: {
  code: 200,
  data: true/false,
  message: '用户已登录/未登录'
}
```

## 📋 路由配置

### 固定路由（白名单）
```javascript
[
  '/login',      // 登录页
  '/register',   // 注册页
  '/contact',    // 联系我们
  '/home',       // 首页
  '/403',        // 权限不足
  '/404',        // 页面不存在
  '/500'         // 服务器错误
]
```

### 动态路由示例
```javascript
// 后端返回的路由数据格式
{
  path: '',
  component: 'Layout',
  redirect: '/get-mailbox',
  children: [
    {
      path: '/get-mailbox',
      component: 'GetMailbox/index',
      name: 'GetMailbox',
      meta: { 
        title: '获取邮箱', 
        icon: 'mail',
        affix: false
      }
    }
  ]
}
```

## 🔄 工作流程

```mermaid
graph TD
    A[用户访问页面] --> B{检查token}
    B -->|无token| C[白名单检查]
    C -->|在白名单| D[直接访问]
    C -->|不在白名单| E[跳转登录]
    B -->|有token| F{检查用户信息}
    F -->|有用户信息| G[直接访问]
    F -->|无用户信息| H[获取用户信息]
    H --> I[生成动态路由]
    I --> J[添加到Router]
    J --> K[重新导航]
```

## 💻 使用方法

### 1. 在组件中使用
```javascript
import { usePermissionStore, useUserStore } from '@/store'

const permissionStore = usePermissionStore()
const userStore = useUserStore()

// 获取菜单
const menus = permissionStore.getMenuTree()

// 检查权限
const hasAccess = permissionStore.isWhiteList('/some-path')
```

### 2. 手动操作
```javascript
// 重新生成路由
await permissionStore.generateRoutes()

// 清空路由
permissionStore.clearRoutes()

// 获取用户信息
await userStore.getUserInfo()
```

## 🧪 测试

访问 `/test/dynamic-route` 页面可以测试动态路由功能，包括：
- 查看当前路由信息
- 查看用户权限信息
- 测试路由生成功能
- 测试登出功能

## ✅ 完成状态

- ✅ 路由系统重构完成
- ✅ 权限管理系统完成
- ✅ 路由守卫系统完成
- ✅ API接口集成完成
- ✅ 错误页面创建完成
- ✅ Layout布局完成
- ✅ 工具函数库完成
- ✅ 测试页面创建完成
- ✅ 文档编写完成

## 🎉 项目状态

**✅ 动态路由系统已完全实现并可正常运行！**

项目现在具备了完整的动态路由功能，可以根据用户权限动态加载路由和菜单，完全符合tellyou的设计标准。
