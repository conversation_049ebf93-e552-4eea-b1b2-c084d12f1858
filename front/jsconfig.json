{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": false, "lib": ["ESNext", "DOM"], "jsx": "preserve", "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["./src/*", "./mock/*"]}}, "include": ["mock/*.js", "src/**/*.js", "src/**/*.vue", "vite.config.js"], "exclude": ["dist", "node_modules"]}