# Redis集成说明文档

## 概述

本项目已成功集成Redis缓存系统，主要用于用户会话管理和token存储。Redis配置为使用localhost:6379，密码123456，数据存储在db2中。

## 功能特性

### 1. 用户Token管理
- ✅ 用户登录/注册时自动将token存储到Redis
- ✅ Token过期时间设置为5小时（18000秒）
- ✅ 用户登出时自动清除Redis中的token记录
- ✅ 支持token验证和刷新功能

### 2. Redis配置
- **主机**: localhost:6379
- **密码**: 123456
- **数据库**: db2
- **连接池**: 支持自动重连
- **错误处理**: 完善的错误处理和日志记录

## 文件结构

```
server/
├── config/
│   └── redis.js              # Redis连接配置
├── utils/
│   └── redisUtils.js          # Redis工具类
├── middleware/
│   └── redisAuth.js           # Redis认证中间件
├── routes/
│   └── auth.js                # 已集成Redis的认证路由
├── .env.example               # 环境变量示例
└── test-redis.js              # Redis功能测试脚本
```

## 主要功能

### RedisUtils类方法

#### 用户Token管理
- `setUserToken(userId, token, ttl)` - 存储用户token
- `getUserToken(userId)` - 获取用户token信息
- `validateUserToken(userId, token)` - 验证token是否有效
- `deleteUserToken(userId)` - 删除用户token
- `getUserTokenTTL(userId)` - 获取token剩余时间
- `refreshUserToken(userId, ttl)` - 刷新token过期时间

#### 基础缓存操作
- `set(key, value, ttl)` - 设置缓存
- `get(key)` - 获取缓存
- `del(key)` - 删除缓存
- `exists(key)` - 检查键是否存在

## 使用示例

### 1. 用户登录时存储Token

```javascript
// 在auth.js中
const token = generateToken({
  userId: user.id,
  username: user.username,
  email: user.email
});

// 存储到Redis，过期时间5小时
await redisUtils.setUserToken(user.id, token, 18000);
```

### 2. 用户登出时清除Token

```javascript
// 在logout路由中
const userId = req.user.id;
const deleted = await redisUtils.deleteUserToken(userId);
```

### 3. 验证Token

```javascript
// 使用Redis认证中间件
const { redisAuthMiddleware } = require('../middleware/redisAuth');

// 在需要认证的路由中使用
router.get('/protected', redisAuthMiddleware, (req, res) => {
  // 已通过Redis token验证的逻辑
});
```

## 环境变量配置

在`.env`文件中配置以下变量：

```env
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=123456
REDIS_DB=2
```

## 健康检查

服务器启动后，可以通过以下接口检查Redis连接状态：

```
GET /api/health
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "status": "OK",
    "database": "连接正常",
    "redis": "连接正常",
    "timestamp": "2025-08-02T07:29:18.234Z"
  },
  "message": "服务器健康状态良好"
}
```

## 测试

运行Redis功能测试：

```bash
cd server
node test-redis.js
```

## 安全特性

1. **Token过期管理**: 自动过期机制，防止长期有效的token
2. **用户状态检查**: 验证时检查用户是否被禁用
3. **自动清理**: 用户被删除或禁用时自动清理相关token
4. **连接安全**: 使用密码保护的Redis连接

## 性能优化

1. **连接池**: 使用连接池管理Redis连接
2. **自动重连**: 网络中断时自动重连
3. **错误处理**: 优雅的错误处理，不影响主要业务流程
4. **日志记录**: 详细的操作日志，便于调试和监控

## 注意事项

1. 确保Redis服务器正在运行
2. 检查防火墙设置，确保端口6379可访问
3. 定期备份Redis数据（如果需要持久化）
4. 监控Redis内存使用情况
5. 在生产环境中使用更强的密码

## 故障排除

### 常见问题

1. **连接失败**: 检查Redis服务是否启动，端口是否正确
2. **认证失败**: 检查密码配置是否正确
3. **数据库选择错误**: 确认使用的是db2

### 日志查看

Redis相关的日志会输出到控制台，包括：
- 连接状态
- 操作结果
- 错误信息

## 后续扩展

可以考虑添加以下功能：
1. 分布式锁
2. 消息队列
3. 限流功能
4. 缓存预热
5. 数据统计
