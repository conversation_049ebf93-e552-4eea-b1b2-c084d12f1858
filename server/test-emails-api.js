const axios = require('axios');

const BASE_URL = 'http://localhost:9090/api/emails';

// 测试用的邮件数据
const testEmailData = {
  message_id: 'test-' + Date.now() + '@example.com',
  from_address: '<EMAIL>',
  to_address: '<EMAIL>',
  subject: '测试邮件主题',
  sent_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
  received_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
  html_content: '<p>这是一个测试邮件内容</p>',
  verification_code: '123456'
};

async function testEmailsAPI() {
  console.log('🧪 开始测试 Emails API...\n');

  try {
    // 1. 测试获取邮件列表
    console.log('1️⃣ 测试获取邮件列表...');
    const listResponse = await axios.get(BASE_URL);
    console.log('✅ 获取邮件列表成功:', listResponse.data.message);
    console.log('📊 邮件总数:', listResponse.data.data.pagination.total);
    console.log('');

    // 2. 测试创建邮件
    console.log('2️⃣ 测试创建邮件...');
    const createResponse = await axios.post(BASE_URL, testEmailData);
    console.log('✅ 创建邮件成功:', createResponse.data.message);
    const createdEmailId = createResponse.data.data.id;
    console.log('📧 创建的邮件ID:', createdEmailId);
    console.log('');

    // 3. 测试获取邮件详情
    console.log('3️⃣ 测试获取邮件详情...');
    const detailResponse = await axios.get(`${BASE_URL}/${createdEmailId}`);
    console.log('✅ 获取邮件详情成功:', detailResponse.data.message);
    console.log('📧 邮件主题:', detailResponse.data.data.subject);
    console.log('');

    // 4. 测试更新邮件
    console.log('4️⃣ 测试更新邮件...');
    const updateData = {
      subject: '更新后的邮件主题',
      verification_code: '654321'
    };
    const updateResponse = await axios.put(`${BASE_URL}/${createdEmailId}`, updateData);
    console.log('✅ 更新邮件成功:', updateResponse.data.message);
    console.log('📧 更新后的主题:', updateResponse.data.data.subject);
    console.log('🔑 更新后的验证码:', updateResponse.data.data.verification_code);
    console.log('');

    // 5. 测试搜索邮件
    console.log('5️⃣ 测试搜索邮件...');
    const searchResponse = await axios.get(`${BASE_URL}/search?keyword=更新后`);
    console.log('✅ 搜索邮件成功:', searchResponse.data.message);
    console.log('🔍 搜索结果数量:', searchResponse.data.data.pagination.total);
    console.log('');

    // 6. 测试获取验证码邮件
    console.log('6️⃣ 测试获取验证码邮件...');
    const verificationResponse = await axios.get(`${BASE_URL}/verification-codes`);
    console.log('✅ 获取验证码邮件成功:', verificationResponse.data.message);
    console.log('🔑 验证码邮件数量:', verificationResponse.data.data.pagination.total);
    console.log('');

    // 7. 测试获取邮件统计
    console.log('7️⃣ 测试获取邮件统计...');
    const statsResponse = await axios.get(`${BASE_URL}/stats/overview`);
    console.log('✅ 获取邮件统计成功:', statsResponse.data.message);
    console.log('📊 统计信息:', {
      总邮件数: statsResponse.data.data.overview.total,
      今日邮件: statsResponse.data.data.overview.today,
      验证码邮件: statsResponse.data.data.overview.verification
    });
    console.log('');

    // 8. 测试删除邮件
    console.log('8️⃣ 测试删除邮件...');
    const deleteResponse = await axios.delete(`${BASE_URL}/${createdEmailId}`);
    console.log('✅ 删除邮件成功:', deleteResponse.data.message);
    console.log('');

    // 9. 测试批量删除（创建多个邮件然后批量删除）
    console.log('9️⃣ 测试批量删除邮件...');
    const emailIds = [];
    for (let i = 0; i < 3; i++) {
      const batchEmailData = {
        ...testEmailData,
        message_id: `batch-test-${Date.now()}-${i}@example.com`,
        subject: `批量测试邮件 ${i + 1}`,
        sent_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
        received_time: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };
      const batchCreateResponse = await axios.post(BASE_URL, batchEmailData);
      emailIds.push(batchCreateResponse.data.data.id);
    }
    
    const batchDeleteResponse = await axios.delete(BASE_URL, {
      data: { ids: emailIds }
    });
    console.log('✅ 批量删除邮件成功:', batchDeleteResponse.data.message);
    console.log('🗑️ 删除数量:', batchDeleteResponse.data.data.deletedCount);
    console.log('');

    console.log('🎉 所有 Emails API 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testEmailsAPI();
