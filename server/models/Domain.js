const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Domain {
  // 创建域名
  static async create(domainData) {
    const { domain_name, status, description } = domainData;
    const id = uuidv4();

    const sql = 'INSERT INTO domains (id, domain_name, status, description) VALUES (?, ?, ?, ?)';

    try {
      console.log('📝 创建域名 - 输入数据:', domainData);
      console.log('📝 执行SQL:', sql);
      console.log('📝 SQL参数:', [id, domain_name, status || 1, description || '']);

      await query(sql, [id, domain_name, status || 1, description || '']);

      console.log('✅ 域名创建成功，ID:', id);
      return await this.findById(id);
    } catch (error) {
      console.error('❌ 创建域名失败:', error.message);
      console.error('❌ SQL错误详情:', error);
      console.error('❌ 错误堆栈:', error.stack);
      throw error;
    }
  }

  // 根据ID查找域名
  static async findById(id) {
    const sql = 'SELECT * FROM domains WHERE id = ?';
    try {
      const results = await query(sql, [id]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error('根据ID查找域名失败:', error.message);
      throw error;
    }
  }

  // 根据域名名称查找
  static async findByDomainName(domainName) {
    const sql = 'SELECT * FROM domains WHERE domain_name = ?';
    try {
      const results = await query(sql, [domainName]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error('根据域名名称查找失败:', error.message);
      throw error;
    }
  }

  // 获取所有域名（分页）
  static async findAll(page = 1, pageSize = 10, filters = {}) {
    let whereClause = 'WHERE 1=1';
    let params = [];

    // 构建筛选条件
    if (filters.domain_name) {
      whereClause += ' AND domain_name LIKE ?';
      params.push(`%${filters.domain_name}%`);
    }

    if (filters.status !== undefined && filters.status !== '') {
      whereClause += ' AND status = ?';
      params.push(filters.status);
    }

    // 计算总数
    const countSql = `SELECT COUNT(*) as total FROM domains ${whereClause}`;
    const countResult = await query(countSql, params);
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const dataSql = `
      SELECT * FROM domains 
      ${whereClause} 
      ORDER BY create_time DESC 
      LIMIT ? OFFSET ?
    `;
    
    const dataParams = [...params, pageSize, offset];
    const list = await query(dataSql, dataParams);

    return {
      list,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    };
  }

  // 更新域名
  static async update(id, domainData) {
    const { domain_name, status, description } = domainData;
    const sql = 'UPDATE domains SET domain_name = ?, status = ?, description = ? WHERE id = ?';

    try {
      console.log('📝 更新域名 - ID:', id);
      console.log('📝 更新数据:', domainData);

      const result = await query(sql, [domain_name, status, description, id]);
      
      if (result.affectedRows === 0) {
        console.log('❌ 域名不存在或未更新');
        return null;
      }

      console.log('✅ 域名更新成功');
      return await this.findById(id);
    } catch (error) {
      console.error('❌ 更新域名失败:', error.message);
      throw error;
    }
  }

  // 删除域名
  static async delete(id) {
    const sql = 'DELETE FROM domains WHERE id = ?';
    try {
      console.log('📝 删除域名 - ID:', id);
      const result = await query(sql, [id]);
      const success = result.affectedRows > 0;
      
      if (success) {
        console.log('✅ 域名删除成功');
      } else {
        console.log('❌ 域名不存在或删除失败');
      }
      
      return success;
    } catch (error) {
      console.error('❌ 删除域名失败:', error.message);
      throw error;
    }
  }

  // 批量删除域名
  static async batchDelete(ids) {
    if (!ids || ids.length === 0) {
      return false;
    }

    const placeholders = ids.map(() => '?').join(',');
    const sql = `DELETE FROM domains WHERE id IN (${placeholders})`;
    
    try {
      console.log('📝 批量删除域名 - IDs:', ids);
      const result = await query(sql, ids);
      const success = result.affectedRows > 0;
      
      if (success) {
        console.log('✅ 批量删除域名成功，删除数量:', result.affectedRows);
      } else {
        console.log('❌ 批量删除域名失败');
      }
      
      return success;
    } catch (error) {
      console.error('❌ 批量删除域名失败:', error.message);
      throw error;
    }
  }

  // 更新域名状态
  static async updateStatus(id, status) {
    const sql = 'UPDATE domains SET status = ? WHERE id = ?';
    try {
      console.log('📝 更新域名状态 - ID:', id, '状态:', status);
      const result = await query(sql, [status, id]);
      
      if (result.affectedRows === 0) {
        console.log('❌ 域名不存在或状态未更新');
        return null;
      }

      console.log('✅ 域名状态更新成功');
      return await this.findById(id);
    } catch (error) {
      console.error('❌ 更新域名状态失败:', error.message);
      throw error;
    }
  }

  // 获取启用的域名列表
  static async findEnabled() {
    const sql = 'SELECT * FROM domains WHERE status = 1 ORDER BY create_time DESC';
    try {
      const results = await query(sql);
      return results;
    } catch (error) {
      console.error('获取启用域名列表失败:', error.message);
      throw error;
    }
  }
}

module.exports = Domain;
