const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Permission {
  // 创建权限
  static async create(permissionData) {
    const { code, name, description, group_code, group_name, sort_order, status } = permissionData;
    const id = uuidv4();

    const sql = `
      INSERT INTO permissions (id, code, name, description, group_code, group_name, sort_order, status)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    try {
      await query(sql, [
        id,
        code,
        name,
        description || '',
        group_code,
        group_name,
        sort_order || 0,
        status || 1
      ]);
      return await this.findById(id);
    } catch (error) {
      console.error('创建权限失败:', error.message);
      throw error;
    }
  }

  // 根据ID查找权限
  static async findById(id) {
    const sql = 'SELECT * FROM permissions WHERE id = ?';
    try {
      const permissions = await query(sql, [id]);
      return permissions.length > 0 ? permissions[0] : null;
    } catch (error) {
      console.error('根据ID查找权限失败:', error.message);
      throw error;
    }
  }

  // 根据权限代码查找权限
  static async findByCode(code) {
    const sql = 'SELECT * FROM permissions WHERE code = ?';
    try {
      const permissions = await query(sql, [code]);
      return permissions.length > 0 ? permissions[0] : null;
    } catch (error) {
      console.error('根据代码查找权限失败:', error.message);
      throw error;
    }
  }

  // 获取所有权限（分页）
  static async findAll(page = 1, pageSize = 10) {
    const offset = (page - 1) * pageSize;
    
    try {
      // 获取总数
      const countSql = 'SELECT COUNT(*) as total FROM permissions';
      const countResult = await query(countSql);
      const total = countResult[0].total;

      // 获取权限列表
      const sql = `
        SELECT * FROM permissions 
        ORDER BY group_code ASC, sort_order ASC, create_time ASC
        LIMIT ? OFFSET ?
      `;
      const permissions = await query(sql, [pageSize, offset]);

      return {
        list: permissions,
        total: total,
        page: page,
        pageSize: pageSize
      };
    } catch (error) {
      console.error('获取权限列表失败:', error.message);
      throw error;
    }
  }

  // 获取所有权限（按分组）
  static async findAllGrouped() {
    const sql = `
      SELECT * FROM permissions 
      WHERE status = 1
      ORDER BY group_code ASC, sort_order ASC, create_time ASC
    `;
    
    try {
      const permissions = await query(sql);
      
      // 按分组整理权限
      const grouped = {};
      permissions.forEach(permission => {
        if (!grouped[permission.group_code]) {
          grouped[permission.group_code] = {
            group_code: permission.group_code,
            group_name: permission.group_name,
            permissions: []
          };
        }
        grouped[permission.group_code].permissions.push(permission);
      });

      return Object.values(grouped);
    } catch (error) {
      console.error('获取分组权限失败:', error.message);
      throw error;
    }
  }

  // 更新权限
  static async update(id, permissionData) {
    const { code, name, description, group_code, group_name, sort_order, status } = permissionData;
    const sql = `
      UPDATE permissions 
      SET code = ?, name = ?, description = ?, group_code = ?, group_name = ?, sort_order = ?, status = ?
      WHERE id = ?
    `;

    try {
      const result = await query(sql, [code, name, description, group_code, group_name, sort_order, status, id]);
      if (result.affectedRows === 0) {
        return null;
      }
      return await this.findById(id);
    } catch (error) {
      console.error('更新权限失败:', error.message);
      throw error;
    }
  }

  // 删除权限
  static async delete(id) {
    const sql = 'DELETE FROM permissions WHERE id = ?';
    try {
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除权限失败:', error.message);
      throw error;
    }
  }

  // 获取权限分组列表
  static async getGroups() {
    const sql = `
      SELECT DISTINCT group_code, group_name 
      FROM permissions 
      WHERE status = 1
      ORDER BY group_code ASC
    `;
    
    try {
      const groups = await query(sql);
      return groups;
    } catch (error) {
      console.error('获取权限分组失败:', error.message);
      throw error;
    }
  }
}

module.exports = Permission;
