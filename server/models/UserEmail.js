const { query, transaction } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class UserEmail {

  // 创建用户邮箱关联
  static async create(userEmailData) {
    const { user_id, email_address } = userEmailData;

    // 验证必填字段
    if (!user_id || !email_address) {
      throw new Error('缺少必填字段：user_id, email_address');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email_address)) {
      throw new Error('邮箱格式不正确');
    }

    const id = uuidv4();

    const sql = `
      INSERT INTO user_emails (id, user_id, email_address)
      VALUES (?, ?, ?)
    `;

    const params = [
      id,
      user_id,
      email_address.toLowerCase().trim()
    ];

    try {
      console.log('📝 创建用户邮箱关联 - 输入数据:', userEmailData);
      console.log('📝 执行SQL:', sql);
      console.log('📝 SQL参数:', params);

      await query(sql, params);

      console.log('✅ 用户邮箱关联创建成功，ID:', id);
      return await this.findById(id);
    } catch (error) {
      console.error('❌ 创建用户邮箱关联失败:', error.message);

      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('该邮箱已经添加到您的邮箱列表中');
      }

      throw error;
    }
  }

  // 根据ID查找用户邮箱
  static async findById(id) {
    const sql = 'SELECT * FROM user_emails WHERE id = ?';

    try {
      const userEmails = await query(sql, [id]);
      return userEmails[0] || null;
    } catch (error) {
      console.error('查找用户邮箱失败:', error.message);
      throw error;
    }
  }

  // 根据用户ID获取邮箱列表（支持分页）
  static async findByUserId(userId, page = 1, pageSize = 10) {
    try {
      // 计算总数
      const countSql = 'SELECT COUNT(*) as total FROM user_emails WHERE user_id = ?';
      const countResult = await query(countSql, [userId]);
      const total = countResult[0].total;

      // 获取分页数据
      const offset = (page - 1) * pageSize;
      const dataSql = `
        SELECT * FROM user_emails
        WHERE user_id = ?
        ORDER BY created_time DESC
        LIMIT ? OFFSET ?
      `;

      const list = await query(dataSql, [userId, pageSize, offset]);

      return {
        list,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      };
    } catch (error) {
      console.error('根据用户ID获取邮箱列表失败:', error.message);
      throw error;
    }
  }

  // 检查用户是否已添加某个邮箱
  static async findByUserIdAndEmail(userId, emailAddress) {
    const sql = 'SELECT * FROM user_emails WHERE user_id = ? AND email_address = ?';
    try {
      const results = await query(sql, [userId, emailAddress.toLowerCase().trim()]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error('检查用户邮箱关联失败:', error.message);
      throw error;
    }
  }
  
  // 删除用户邮箱关联
  static async delete(id) {
    const sql = 'DELETE FROM user_emails WHERE id = ?';
    try {
      console.log('📝 删除用户邮箱关联 - ID:', id);
      const result = await query(sql, [id]);

      if (result.affectedRows === 0) {
        console.log('❌ 用户邮箱关联不存在');
        return false;
      }

      console.log('✅ 用户邮箱关联删除成功');
      return true;
    } catch (error) {
      console.error('❌ 删除用户邮箱关联失败:', error.message);
      throw error;
    }
  }

  // 根据用户ID和邮箱地址删除
  static async deleteByUserIdAndEmail(userId, emailAddress) {
    const sql = 'DELETE FROM user_emails WHERE user_id = ? AND email_address = ?';
    try {
      console.log('📝 删除用户邮箱关联 - 用户ID:', userId, '邮箱:', emailAddress);
      const result = await query(sql, [userId, emailAddress.toLowerCase().trim()]);

      if (result.affectedRows === 0) {
        console.log('❌ 用户邮箱关联不存在');
        return false;
      }

      console.log('✅ 用户邮箱关联删除成功');
      return true;
    } catch (error) {
      console.error('❌ 删除用户邮箱关联失败:', error.message);
      throw error;
    }
  }
}

module.exports = UserEmail;
