# MailCode服务器启动指南

## 概述

MailCode服务器现在具备完整的启动前检查机制，确保MySQL和Redis服务正确连接后才启动HTTP服务器。

## 启动要求

在启动服务器之前，必须确保以下服务正常运行：

### 1. MySQL数据库
- **地址**: localhost:3306
- **数据库**: mailcode (或mail-code)
- **用户**: root
- **密码**: 根据你的配置

### 2. Redis服务器
- **地址**: localhost:6379
- **密码**: 123456
- **数据库**: db2

## 启动命令

### 安全启动（推荐）
```bash
cd server
npm start
```

这个命令会：
1. 🔍 检查MySQL连接
2. 🔍 检查Redis连接
3. ✅ 确认所有服务正常后启动HTTP服务器
4. ❌ 如果任何服务异常，则拒绝启动并显示详细错误信息

### 其他启动选项
```bash
# 直接启动（跳过检查）
npm run start:direct

# 开发模式（自动重启）
npm run dev

# 开发模式（带安全检查）
npm run dev:safe

# 仅检查服务状态
npm run check
```

## 启动流程

### 成功启动示例
```
🛡️  MailCode安全启动程序
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚀 开始检查服务连接状态...
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔍 检查MySQL连接...
✅ MySQL连接正常
🔍 检查Redis连接...
✅ Redis连接正常
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎉 所有服务连接正常，可以启动服务器！

🚀 所有服务检查通过，正在启动服务器...
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🚀 开始启动MailCode服务器...
📊 正在连接MySQL数据库...
✅ MySQL数据库连接成功
🔴 正在连接Redis服务器...
✅ Redis连接成功
🌐 正在启动HTTP服务器...

🎉 MailCode服务器启动成功！
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📍 服务器地址: http://localhost:9090
🔗 健康检查: http://localhost:9090/api/health
📋 API文档: http://localhost:9090/
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 MySQL: 连接正常
🔴 Redis: 连接正常
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✨ 服务器已就绪，等待请求...
```

## 接口状态检查

### 根路径接口 (/)
```bash
curl http://localhost:9090/
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "version": "1.0.0",
    "timestamp": "2025-08-02T07:39:00.449Z",
    "services": {
      "mysql": {
        "status": "connected",
        "message": "MySQL连接正常"
      },
      "redis": {
        "status": "connected",
        "message": "Redis连接正常"
      }
    }
  },
  "message": "MailCode API服务器运行中"
}
```

### 健康检查接口 (/api/health)
```bash
curl http://localhost:9090/api/health
```

响应示例：
```json
{
  "code": 200,
  "data": {
    "status": "OK",
    "database": "连接正常",
    "redis": "连接正常",
    "timestamp": "2025-08-02T07:39:06.634Z"
  },
  "message": "服务器健康状态良好"
}
```

## 故障排除

### 启动失败的常见原因

#### MySQL连接失败
```
❌ MySQL连接失败: connect ECONNREFUSED 127.0.0.1:3306
```

**解决方案**：
1. 检查MySQL服务是否启动
2. 检查端口3306是否开放
3. 检查数据库配置（用户名、密码、数据库名）
4. 检查防火墙设置

#### Redis连接失败
```
❌ Redis连接失败: connect ECONNREFUSED 127.0.0.1:6379
```

**解决方案**：
1. 检查Redis服务是否启动
2. 检查端口6379是否开放
3. 检查Redis密码是否为123456
4. 检查是否可以访问db2

#### 端口占用
```
Error: listen EADDRINUSE: address already in use :::9090
```

**解决方案**：
1. 查找占用端口的进程：`netstat -ano | findstr :9090`
2. 终止进程：`taskkill /PID <PID> /F`
3. 或者修改端口配置

## 环境变量配置

创建 `.env` 文件：
```env
# 服务器配置
PORT=9090
NODE_ENV=development

# JWT配置
JWT_SECRET=your-secret-key

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-password
DB_NAME=mailcode

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=123456
REDIS_DB=2
```

## 优雅关闭

使用 `Ctrl+C` 关闭服务器时，系统会：
1. 🛑 接收关闭信号
2. 🔴 关闭Redis连接
3. 📊 清理数据库连接
4. 👋 安全退出

## 开发建议

1. **开发环境**: 使用 `npm run dev` 进行开发，支持自动重启
2. **生产环境**: 使用 `npm start` 确保启动前检查
3. **调试**: 使用 `npm run check` 单独检查服务状态
4. **监控**: 定期访问 `/api/health` 检查服务健康状态
