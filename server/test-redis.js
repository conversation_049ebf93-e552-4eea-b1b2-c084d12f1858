const { redisClient } = require('./config/redis');
const redisUtils = require('./utils/redisUtils');

async function testRedis() {
  try {
    console.log('开始测试Redis连接和功能...');
    
    // 连接Redis
    await redisClient.connect();
    
    // 测试基本的set/get操作
    console.log('\n1. 测试基本缓存操作:');
    await redisUtils.set('test_key', 'Hello Redis!', 60);
    const value = await redisUtils.get('test_key');
    console.log('设置值:', 'Hello Redis!');
    console.log('获取值:', value);
    
    // 测试用户token功能
    console.log('\n2. 测试用户token功能:');
    const userId = 'test_user_123';
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
    
    // 存储token
    await redisUtils.setUserToken(userId, token, 300); // 5分钟过期
    console.log('Token已存储');
    
    // 获取token信息
    const tokenData = await redisUtils.getUserToken(userId);
    console.log('Token信息:', tokenData);
    
    // 验证token
    const isValid = await redisUtils.validateUserToken(userId, token);
    console.log('Token验证结果:', isValid);
    
    // 获取TTL
    const ttl = await redisUtils.getUserTokenTTL(userId);
    console.log('Token剩余时间(秒):', ttl);
    
    // 刷新token
    await redisUtils.refreshUserToken(userId, 600); // 延长到10分钟
    const newTtl = await redisUtils.getUserTokenTTL(userId);
    console.log('刷新后剩余时间(秒):', newTtl);
    
    // 删除token
    const deleted = await redisUtils.deleteUserToken(userId);
    console.log('Token删除结果:', deleted);
    
    // 验证删除后的状态
    const tokenAfterDelete = await redisUtils.getUserToken(userId);
    console.log('删除后的Token信息:', tokenAfterDelete);
    
    console.log('\n✅ Redis测试完成！所有功能正常工作。');
    
  } catch (error) {
    console.error('❌ Redis测试失败:', error.message);
  } finally {
    // 断开连接
    await redisClient.disconnect();
    console.log('Redis连接已断开');
    process.exit(0);
  }
}

// 运行测试
testRedis();
