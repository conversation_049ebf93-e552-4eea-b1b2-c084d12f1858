const { generateToken, verifyToken } = require('./utils/jwt');
const { redisClient } = require('./config/redis');
const redisUtils = require('./utils/redisUtils');

async function testSimplifiedTokenStructure() {
  try {
    console.log('🧪 开始测试简化的Token存储结构...');
    console.log('📋 新结构: token作为键，用户ID字符串作为值（不是JSON对象）');
    
    // 连接Redis
    await redisClient.connect();
    
    // 测试用户ID
    const userId = 'user_simplified_123';
    
    console.log('\n1. 生成JWT token:');
    const token = generateToken(userId);
    console.log('用户ID:', userId);
    console.log('生成的Token:', token.substring(0, 50) + '...');
    
    console.log('\n2. 存储到Redis（token为键，用户ID字符串为值）:');
    const stored = await redisUtils.setToken(token, userId, 300); // 5分钟过期
    console.log('存储结果:', stored);
    
    console.log('\n3. 直接从Redis获取用户ID:');
    const userIdFromRedis = await redisUtils.getUserIdByToken(token);
    console.log('从Redis获取的用户ID:', userIdFromRedis);
    console.log('数据类型:', typeof userIdFromRedis);
    console.log('验证结果:', userIdFromRedis === userId ? '✅ 匹配' : '❌ 不匹配');
    
    console.log('\n4. 使用validateToken方法:');
    const validatedUserId = await redisUtils.validateToken(token);
    console.log('验证获取的用户ID:', validatedUserId);
    console.log('验证结果:', validatedUserId === userId ? '✅ 匹配' : '❌ 不匹配');
    
    console.log('\n5. 检查Redis中的原始数据:');
    const rawValue = await redisClient.getClient().get(`token:${token}`);
    console.log('Redis原始值:', rawValue);
    console.log('原始值类型:', typeof rawValue);
    console.log('是否为纯字符串:', rawValue === userId ? '✅ 是' : '❌ 否');
    
    console.log('\n6. 检查token过期时间:');
    const ttl = await redisUtils.getTokenTTL(token);
    console.log('剩余时间(秒):', ttl);
    
    console.log('\n7. 刷新token过期时间:');
    await redisUtils.refreshToken(token, 600); // 延长到10分钟
    const newTtl = await redisUtils.getTokenTTL(token);
    console.log('刷新后剩余时间(秒):', newTtl);
    
    console.log('\n8. 测试多个token（同一用户）:');
    const token2 = generateToken(userId);
    await redisUtils.setToken(token2, userId, 300);
    console.log('第二个token已存储');
    
    const userId1 = await redisUtils.validateToken(token);
    const userId2 = await redisUtils.validateToken(token2);
    console.log('Token1验证:', userId1);
    console.log('Token2验证:', userId2);
    
    console.log('\n9. 测试不同用户的token:');
    const anotherUserId = 'user_another_456';
    const anotherToken = generateToken(anotherUserId);
    await redisUtils.setToken(anotherToken, anotherUserId, 300);
    
    const anotherUserIdFromRedis = await redisUtils.validateToken(anotherToken);
    console.log('另一个用户ID验证:', anotherUserIdFromRedis);
    
    console.log('\n10. 删除特定token（精确登出）:');
    const deleted = await redisUtils.deleteToken(token);
    console.log('Token删除结果:', deleted);
    
    console.log('\n11. 验证删除后的状态:');
    const userIdAfterDelete = await redisUtils.validateToken(token);
    const userId2AfterDelete = await redisUtils.validateToken(token2);
    const anotherUserAfterDelete = await redisUtils.validateToken(anotherToken);
    console.log('删除后Token1验证:', userIdAfterDelete);
    console.log('删除后Token2验证:', userId2AfterDelete);
    console.log('另一个用户Token验证:', anotherUserAfterDelete);
    
    console.log('\n12. 删除用户的所有token:');
    const deletedCount = await redisUtils.deleteUserTokens(userId);
    console.log('删除的token数量:', deletedCount);
    
    console.log('\n13. 最终验证:');
    const finalCheck1 = await redisUtils.validateToken(token2);
    const finalCheck2 = await redisUtils.validateToken(anotherToken);
    console.log('最终Token2验证:', finalCheck1);
    console.log('另一个用户最终验证:', finalCheck2);
    
    console.log('\n✅ 简化Token存储结构测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   - Token作为Redis键，用户ID字符串作为值 ✅');
    console.log('   - 不存储JSON对象，只存储纯字符串 ✅');
    console.log('   - 支持通过token直接获取用户ID ✅');
    console.log('   - 支持同一用户多个token并存 ✅');
    console.log('   - 支持删除特定token（精确登出）✅');
    console.log('   - 支持删除用户所有token（全局登出）✅');
    console.log('   - Token过期时间控制正常 ✅');
    console.log('   - 存储空间更小，性能更好 ✅');
    
    console.log('\n🔄 Redis存储结构:');
    console.log('   键: token:{jwt_token}');
    console.log('   值: user_123 (纯字符串，不是JSON)');
    console.log('   TTL: 5小时（18000秒）');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 断开连接
    await redisClient.disconnect();
    console.log('\nRedis连接已断开');
    process.exit(0);
  }
}

// 运行测试
testSimplifiedTokenStructure();
