const { checkAllServices } = require('./check-services');
const { spawn } = require('child_process');

/**
 * 安全启动服务器
 * 先检查所有服务连接，确认无误后再启动
 */
async function safeStart() {
  console.log('🛡️  MailCode安全启动程序');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  try {
    // 检查所有服务
    const allServicesOk = await checkAllServices();
    
    if (!allServicesOk) {
      console.log('\n❌ 服务检查失败，无法启动服务器');
      console.log('💡 请解决上述问题后重新运行启动命令');
      process.exit(1);
    }
    
    console.log('\n🚀 所有服务检查通过，正在启动服务器...');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n');
    
    // 启动主应用
    const serverProcess = spawn('node', ['app.js'], {
      stdio: 'inherit',
      cwd: __dirname
    });
    
    // 处理子进程退出
    serverProcess.on('exit', (code) => {
      if (code !== 0) {
        console.log(`\n❌ 服务器进程异常退出，退出码: ${code}`);
      }
      process.exit(code);
    });
    
    // 处理错误
    serverProcess.on('error', (error) => {
      console.error('❌ 启动服务器时出现错误:', error.message);
      process.exit(1);
    });
    
    // 处理中断信号
    process.on('SIGINT', () => {
      console.log('\n🛑 接收到中断信号，正在关闭服务器...');
      serverProcess.kill('SIGINT');
    });
    
    process.on('SIGTERM', () => {
      console.log('\n🛑 接收到终止信号，正在关闭服务器...');
      serverProcess.kill('SIGTERM');
    });
    
  } catch (error) {
    console.error('\n❌ 启动过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 启动
safeStart();
