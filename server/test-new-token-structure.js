const { generateToken, verifyToken } = require('./utils/jwt');
const { redisClient } = require('./config/redis');
const redisUtils = require('./utils/redisUtils');

async function testNewTokenStructure() {
  try {
    console.log('🧪 开始测试新的Token存储结构...');
    console.log('📋 新结构: token作为键，用户ID作为值');
    
    // 连接Redis
    await redisClient.connect();
    
    // 测试用户ID
    const userId = 'user_789';
    
    console.log('\n1. 生成JWT token:');
    const token = generateToken(userId);
    console.log('用户ID:', userId);
    console.log('生成的Token:', token.substring(0, 50) + '...');
    
    // 解析JWT查看内容
    const decoded = verifyToken(token);
    console.log('JWT内容:', {
      userId: decoded.userId,
      type: decoded.type,
      iat: new Date(decoded.iat * 1000).toISOString()
    });
    
    console.log('\n2. 存储到Redis（token为键，用户ID为值）:');
    const stored = await redisUtils.setToken(token, userId, 300); // 5分钟过期
    console.log('存储结果:', stored);
    
    console.log('\n3. 通过token验证并获取用户ID:');
    const userIdFromRedis = await redisUtils.validateToken(token);
    console.log('从Redis获取的用户ID:', userIdFromRedis);
    console.log('验证结果:', userIdFromRedis === userId ? '✅ 匹配' : '❌ 不匹配');
    
    console.log('\n4. 获取token详细信息:');
    const tokenData = await redisUtils.getTokenData(token);
    console.log('Token数据:', tokenData);
    
    console.log('\n5. 检查token过期时间:');
    const ttl = await redisUtils.getTokenTTL(token);
    console.log('剩余时间(秒):', ttl);
    
    console.log('\n6. 刷新token过期时间:');
    await redisUtils.refreshToken(token, 600); // 延长到10分钟
    const newTtl = await redisUtils.getTokenTTL(token);
    console.log('刷新后剩余时间(秒):', newTtl);
    
    console.log('\n7. 测试无效token:');
    const fakeToken = 'fake.jwt.token';
    const fakeUserId = await redisUtils.validateToken(fakeToken);
    console.log('假token验证结果:', fakeUserId);
    
    console.log('\n8. 测试多个token（同一用户）:');
    const token2 = generateToken(userId);
    await redisUtils.setToken(token2, userId, 300);
    console.log('第二个token已存储');
    
    const userId1 = await redisUtils.validateToken(token);
    const userId2 = await redisUtils.validateToken(token2);
    console.log('Token1验证:', userId1);
    console.log('Token2验证:', userId2);
    
    console.log('\n9. 删除特定token（登出）:');
    const deleted = await redisUtils.deleteToken(token);
    console.log('Token删除结果:', deleted);
    
    console.log('\n10. 验证删除后的状态:');
    const userIdAfterDelete = await redisUtils.validateToken(token);
    const userId2AfterDelete = await redisUtils.validateToken(token2);
    console.log('删除后Token1验证:', userIdAfterDelete);
    console.log('删除后Token2验证:', userId2AfterDelete);
    
    console.log('\n11. 删除用户的所有token:');
    const deletedCount = await redisUtils.deleteUserTokens(userId);
    console.log('删除的token数量:', deletedCount);
    
    console.log('\n12. 最终验证:');
    const finalCheck = await redisUtils.validateToken(token2);
    console.log('最终Token2验证:', finalCheck);
    
    console.log('\n✅ 新Token存储结构测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   - Token作为Redis键，用户ID作为值 ✅');
    console.log('   - 支持通过token直接获取用户ID ✅');
    console.log('   - 支持同一用户多个token并存 ✅');
    console.log('   - 支持删除特定token（精确登出）✅');
    console.log('   - 支持删除用户所有token（全局登出）✅');
    console.log('   - Token过期时间控制正常 ✅');
    
    console.log('\n🔄 Redis存储结构:');
    console.log('   键: token:{jwt_token}');
    console.log('   值: {"userId":"user_789","loginTime":"...","expiresAt":"..."}');
    console.log('   TTL: 5小时（18000秒）');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 断开连接
    await redisClient.disconnect();
    console.log('\nRedis连接已断开');
    process.exit(0);
  }
}

// 运行测试
testNewTokenStructure();
