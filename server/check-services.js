const { createConnection } = require('mysql2/promise');
const { createClient } = require('redis');
require('dotenv').config();

/**
 * 检查MySQL连接
 */
async function checkMySQL() {
  try {
    console.log('🔍 检查MySQL连接...');
    
    const connection = await createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'mailcode'
    });

    await connection.ping();
    await connection.end();
    
    console.log('✅ MySQL连接正常');
    return true;
  } catch (error) {
    console.log('❌ MySQL连接失败:', error.message);
    return false;
  }
}

/**
 * 检查Redis连接
 */
async function checkRedis() {
  try {
    console.log('🔍 检查Redis连接...');
    
    const client = createClient({
      socket: {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379
      },
      password: process.env.REDIS_PASSWORD || '123456',
      database: process.env.REDIS_DB || 2
    });

    await client.connect();
    await client.ping();
    await client.quit();
    
    console.log('✅ Redis连接正常');
    return true;
  } catch (error) {
    console.log('❌ Redis连接失败:', error.message);
    return false;
  }
}

/**
 * 主检查函数
 */
async function checkAllServices() {
  console.log('🚀 开始检查服务连接状态...');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  const mysqlOk = await checkMySQL();
  const redisOk = await checkRedis();
  
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  if (mysqlOk && redisOk) {
    console.log('🎉 所有服务连接正常，可以启动服务器！');
    console.log('💡 运行命令: npm run dev 或 npm start');
    return true;
  } else {
    console.log('⚠️  部分服务连接异常，请检查以下项目:');
    
    if (!mysqlOk) {
      console.log('   📊 MySQL问题排查:');
      console.log('      - 检查MySQL服务是否启动');
      console.log('      - 检查端口3306是否开放');
      console.log('      - 检查数据库用户名和密码');
      console.log('      - 检查数据库是否存在');
    }
    
    if (!redisOk) {
      console.log('   🔴 Redis问题排查:');
      console.log('      - 检查Redis服务是否启动');
      console.log('      - 检查端口6379是否开放');
      console.log('      - 检查Redis密码是否为123456');
      console.log('      - 检查是否可以访问db2');
    }
    
    console.log('\n💡 解决问题后重新运行此检查脚本');
    return false;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  checkAllServices()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ 检查过程中出现错误:', error.message);
      process.exit(1);
    });
}

module.exports = {
  checkMySQL,
  checkRedis,
  checkAllServices
};
