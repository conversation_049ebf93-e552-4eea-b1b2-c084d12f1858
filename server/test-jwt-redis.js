const { generateToken, verifyToken } = require('./utils/jwt');
const { redisClient } = require('./config/redis');
const redisUtils = require('./utils/redisUtils');

async function testJWTRedisIntegration() {
  try {
    console.log('🧪 开始测试JWT + Redis集成...');
    
    // 连接Redis
    await redisClient.connect();
    
    // 测试用户ID
    const userId = 'test_user_456';
    
    console.log('\n1. 测试JWT生成（只包含用户ID）:');
    const token = generateToken(userId);
    console.log('生成的Token:', token);
    
    // 解析JWT查看内容
    const decoded = verifyToken(token);
    console.log('JWT内容:', decoded);
    
    console.log('\n2. 测试Redis存储token:');
    await redisUtils.setUserToken(userId, token, 300); // 5分钟过期
    
    console.log('\n3. 测试Redis验证token:');
    const isValid = await redisUtils.validateUserToken(userId, token);
    console.log('Token验证结果:', isValid);
    
    console.log('\n4. 测试获取token信息:');
    const tokenData = await redisUtils.getUserToken(userId);
    console.log('Redis中的token数据:', tokenData);
    
    console.log('\n5. 测试token过期控制:');
    const ttl = await redisUtils.getUserTokenTTL(userId);
    console.log('Token剩余时间(秒):', ttl);
    
    console.log('\n6. 测试刷新token:');
    await redisUtils.refreshUserToken(userId, 600); // 延长到10分钟
    const newTtl = await redisUtils.getUserTokenTTL(userId);
    console.log('刷新后剩余时间(秒):', newTtl);
    
    console.log('\n7. 测试无效token验证:');
    const fakeToken = 'fake.jwt.token';
    const isFakeValid = await redisUtils.validateUserToken(userId, fakeToken);
    console.log('假token验证结果:', isFakeValid);
    
    console.log('\n8. 测试登出（删除token）:');
    const deleted = await redisUtils.deleteUserToken(userId);
    console.log('Token删除结果:', deleted);
    
    console.log('\n9. 验证删除后的状态:');
    const isValidAfterDelete = await redisUtils.validateUserToken(userId, token);
    console.log('删除后token验证结果:', isValidAfterDelete);
    
    console.log('\n✅ JWT + Redis集成测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   - JWT只包含用户ID，不包含敏感信息 ✅');
    console.log('   - JWT过期时间设置为30天，实际由Redis控制 ✅');
    console.log('   - Redis控制token的实际过期时间（5小时）✅');
    console.log('   - 支持token验证、刷新、删除功能 ✅');
    console.log('   - 登出时正确清除Redis中的token ✅');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 断开连接
    await redisClient.disconnect();
    console.log('\nRedis连接已断开');
    process.exit(0);
  }
}

// 运行测试
testJWTRedisIntegration();
