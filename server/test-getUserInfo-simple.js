const { generateToken } = require('./utils/jwt');
const { redisClient } = require('./config/redis');
const redisUtils = require('./utils/redisUtils');

async function testGetUserInfoWithMockToken() {
  try {
    console.log('🧪 开始测试getUserInfo接口（使用模拟token）...');
    
    // 连接Redis
    await redisClient.connect();
    
    // 创建一个模拟的用户ID和token
    const mockUserId = 'test_user_getUserInfo';
    const mockToken = generateToken(mockUserId);
    
    console.log('\n1. 生成模拟token:');
    console.log('用户ID:', mockUserId);
    console.log('Token:', mockToken.substring(0, 50) + '...');
    
    // 将token存储到Redis
    console.log('\n2. 存储token到Redis:');
    await redisUtils.setToken(mockToken, mockUserId, 300);
    console.log('✅ Token已存储到Redis');
    
    // 使用curl测试接口
    console.log('\n3. 测试getUserInfo接口:');
    console.log('执行命令:');
    console.log(`curl -H "Authorization: Bearer ${mockToken}" http://localhost:9090/api/auth/getUserInfo`);
    
    console.log('\n📋 预期行为:');
    console.log('   - Token验证通过 ✅');
    console.log('   - 从数据库查找用户（可能不存在）');
    console.log('   - 如果用户不存在，返回401错误');
    console.log('   - 如果用户存在，返回完整用户信息');
    
    console.log('\n💡 手动测试步骤:');
    console.log('1. 复制上面的curl命令');
    console.log('2. 在新的终端中执行');
    console.log('3. 观察返回结果');
    
    console.log('\n🔍 可能的返回结果:');
    console.log('情况1 - 用户不存在:');
    console.log('{"code":401,"data":null,"message":"用户不存在或已被删除"}');
    
    console.log('\n情况2 - 用户存在:');
    console.log('{');
    console.log('  "code": 200,');
    console.log('  "data": {');
    console.log('    "user": {...},');
    console.log('    "roles": [...],');
    console.log('    "permissions": [...],');
    console.log('    "menus": [...]');
    console.log('  },');
    console.log('  "message": "获取用户信息成功"');
    console.log('}');
    
    // 等待用户测试
    console.log('\n⏳ 等待30秒供手动测试...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // 清理
    console.log('\n4. 清理测试数据:');
    await redisUtils.deleteToken(mockToken);
    console.log('✅ 测试token已删除');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    // 断开连接
    await redisClient.disconnect();
    console.log('\nRedis连接已断开');
    process.exit(0);
  }
}

// 运行测试
testGetUserInfoWithMockToken();
