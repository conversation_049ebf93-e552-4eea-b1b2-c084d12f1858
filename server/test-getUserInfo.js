const axios = require('axios');

// 测试getUserInfo接口
async function testGetUserInfo() {
  const baseURL = 'http://localhost:9090/api';
  
  try {
    console.log('🧪 开始测试getUserInfo接口...');
    
    // 1. 先登录获取token
    console.log('\n1. 用户登录获取token:');
    const loginResponse = await axios.post(`${baseURL}/auth/login`, {
      email: '<EMAIL>', // 请替换为实际的测试用户
      password: 'password123'     // 请替换为实际的测试密码
    });
    
    if (loginResponse.data.code !== 200) {
      console.error('登录失败:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.data.token;
    console.log('登录成功，获取到token:', token.substring(0, 50) + '...');
    
    // 2. 使用token调用getUserInfo接口
    console.log('\n2. 调用getUserInfo接口:');
    const userInfoResponse = await axios.get(`${baseURL}/auth/getUserInfo`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (userInfoResponse.data.code === 200) {
      console.log('✅ getUserInfo接口调用成功');
      
      const data = userInfoResponse.data.data;
      
      console.log('\n📋 返回的数据结构:');
      console.log('用户信息:', {
        id: data.user.id,
        username: data.user.username,
        email: data.user.email,
        mp_open_id: data.user.mp_open_id,
        create_time: data.user.create_time,
        update_time: data.user.update_time,
        status: data.user.status
      });
      
      console.log('角色信息:', data.roles);
      console.log('权限信息:', data.permissions);
      console.log('菜单信息:', data.menus ? `${data.menus.length}个菜单项` : '无菜单');
      
      // 验证数据完整性
      console.log('\n🔍 数据完整性检查:');
      console.log('✅ user对象:', data.user ? '存在' : '❌ 缺失');
      console.log('✅ roles数组:', Array.isArray(data.roles) ? '存在' : '❌ 缺失');
      console.log('✅ permissions数组:', Array.isArray(data.permissions) ? '存在' : '❌ 缺失');
      console.log('✅ menus数组:', Array.isArray(data.menus) ? '存在' : '❌ 缺失');
      
    } else {
      console.error('❌ getUserInfo接口调用失败:', userInfoResponse.data.message);
    }
    
    // 3. 测试无效token
    console.log('\n3. 测试无效token:');
    try {
      await axios.get(`${baseURL}/auth/getUserInfo`, {
        headers: {
          'Authorization': 'Bearer invalid.token.here'
        }
      });
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 无效token正确被拒绝，状态码:', error.response.status);
        console.log('错误信息:', error.response.data.message);
      } else {
        console.error('❌ 无效token测试异常:', error.message);
      }
    }
    
    // 4. 测试无token
    console.log('\n4. 测试无token:');
    try {
      await axios.get(`${baseURL}/auth/getUserInfo`);
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ 无token请求正确被拒绝，状态码:', error.response.status);
        console.log('错误信息:', error.response.data.message);
      } else {
        console.error('❌ 无token测试异常:', error.message);
      }
    }
    
    console.log('\n✅ getUserInfo接口测试完成！');
    console.log('\n📋 测试总结:');
    console.log('   - 合并了/profile和/me方法 ✅');
    console.log('   - 返回完整的用户信息 ✅');
    console.log('   - 返回角色信息 ✅');
    console.log('   - 返回权限信息 ✅');
    console.log('   - 返回菜单信息 ✅');
    console.log('   - Token验证正常工作 ✅');
    console.log('   - 无效token被正确拒绝 ✅');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 检查axios是否安装
try {
  require('axios');
  testGetUserInfo();
} catch (error) {
  console.log('❌ 需要安装axios依赖');
  console.log('请运行: npm install axios');
  console.log('或者使用curl测试:');
  console.log('curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:9090/api/auth/getUserInfo');
}
