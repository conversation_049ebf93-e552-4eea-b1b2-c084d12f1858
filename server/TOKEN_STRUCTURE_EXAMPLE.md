# Token存储结构示例

## 新的存储结构

### Redis键值对结构
```
键: token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
值: user_123 (纯字符串)
TTL: 18000秒（5小时）
```

## 完整的认证流程示例

### 1. 用户登录
```javascript
// 客户端请求
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// 服务器处理
const user = await User.validateLogin(email, password);
const token = generateToken(user.id); // 生成JWT
await redisUtils.setToken(token, user.id, 18000); // 存储到Redis

// 响应
{
  "code": 200,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "message": "登录成功"
}
```

### 2. 访问受保护接口
```javascript
// 客户端请求
GET /api/auth/profile
Headers: {
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

// 服务器验证流程
1. 提取token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
2. 验证JWT格式和签名
3. 从Redis获取用户ID: await redisUtils.validateToken(token)
4. 验证JWT中的用户ID与Redis中的是否一致
5. 刷新token过期时间: await redisUtils.refreshToken(token, 18000)
6. 从数据库获取用户信息
7. 返回用户数据
```

### 3. 用户登出
```javascript
// 客户端请求
POST /api/auth/logout
Headers: {
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

// 服务器处理
const token = req.headers.authorization.substring(7);
await redisUtils.deleteToken(token); // 删除Redis中的token

// 响应
{
  "code": 200,
  "data": null,
  "message": "登出成功"
}
```

## Redis操作示例

### 存储Token
```javascript
// 方法签名
await redisUtils.setToken(token, userId, ttl);

// 实际调用
const token = "eyJhbGciOiJIUzI1NiIs...";
const userId = "user_123";
await redisUtils.setToken(token, userId, 18000);

// Redis中的结果
// 键: token:eyJhbGciOiJIUzI1NiIs...
// 值: user_123 (纯字符串)
```

### 验证Token
```javascript
// 方法签名
const userId = await redisUtils.validateToken(token);

// 实际调用
const token = "eyJhbGciOiJIUzI1NiIs...";
const userId = await redisUtils.validateToken(token);

// 返回结果
// 成功: "user_123" (字符串)
// 失败: null
```

### 获取用户ID
```javascript
// 方法签名
const userId = await redisUtils.getUserIdByToken(token);

// 实际调用
const token = "eyJhbGciOiJIUzI1NiIs...";
const userId = await redisUtils.getUserIdByToken(token);

// 返回结果
// 成功: "user_123" (字符串)
// 失败: null
```

### 删除Token
```javascript
// 删除特定token（单设备登出）
await redisUtils.deleteToken(token);

// 删除用户所有token（全局登出）
await redisUtils.deleteUserTokens(userId);
```

## 多设备登录场景

### 场景：用户在手机和电脑上同时登录
```javascript
// 手机登录
const mobileToken = generateToken("user_123");
await redisUtils.setToken(mobileToken, "user_123", 18000);

// 电脑登录
const desktopToken = generateToken("user_123");
await redisUtils.setToken(desktopToken, "user_123", 18000);

// Redis中的状态
// token:eyJhbGciOiJIUzI1NiIs...(mobile) -> user_123
// token:eyJhbGciOiJIUzI1NiIs...(desktop) -> user_123

// 手机登出（只删除手机token）
await redisUtils.deleteToken(mobileToken);

// 电脑仍然可以正常使用
const userId = await redisUtils.validateToken(desktopToken); // 返回 "user_123"
```

## 管理员功能示例

### 强制用户下线
```javascript
// 管理员强制用户下线（删除所有token）
async function forceUserLogout(userId) {
  const deletedCount = await redisUtils.deleteUserTokens(userId);
  console.log(`用户 ${userId} 的 ${deletedCount} 个会话已被强制下线`);
}

// 调用示例
await forceUserLogout("user_123");
```

### 查看活跃会话
```javascript
// 获取所有活跃token
const activeTokens = await redisClient.getClient().keys('token:*');
console.log('当前活跃会话数:', activeTokens.length);

// 获取特定用户的会话数
async function getUserSessionCount(userId) {
  const allTokens = await redisClient.getClient().keys('token:*');
  let userSessions = 0;
  
  for (const key of allTokens) {
    const storedUserId = await redisClient.getClient().get(key);
    if (storedUserId === userId) {
      userSessions++;
    }
  }
  
  return userSessions;
}
```

## 错误处理示例

### Token验证失败的情况
```javascript
// 1. Token不存在或已过期
const userId = await redisUtils.validateToken("invalid_token");
// 返回: null

// 2. JWT格式错误
try {
  const decoded = verifyToken("malformed.jwt.token");
} catch (error) {
  // 抛出异常: "Token无效"
}

// 3. JWT与Redis不一致
const decoded = verifyToken(token); // JWT中userId: "user_123"
const userIdFromRedis = await redisUtils.validateToken(token); // Redis中userId: "user_456"
// 不一致，拒绝访问
```

## 性能优化建议

### 1. 批量操作
```javascript
// 批量删除过期token（定时任务）
async function cleanupExpiredTokens() {
  // Redis会自动清理过期的键，无需手动处理
  console.log('Redis自动清理过期token');
}
```

### 2. 连接池管理
```javascript
// Redis连接池已在配置中设置
// 避免频繁建立/断开连接
```

### 3. 监控和告警
```javascript
// 监控活跃会话数
setInterval(async () => {
  const activeTokens = await redisClient.getClient().keys('token:*');
  if (activeTokens.length > 10000) {
    console.warn('活跃会话数过多:', activeTokens.length);
  }
}, 60000); // 每分钟检查一次
```

## 总结

新的token存储结构具有以下优势：

1. **直接查找**: 通过token直接获取用户ID，无需反向查找
2. **多设备支持**: 同一用户可以有多个有效token
3. **精确控制**: 可以删除特定token而不影响其他设备
4. **高效验证**: 一次Redis查询即可完成token验证
5. **简化管理**: token作为唯一标识，管理更直观

这种设计更符合现代应用的多设备登录需求，同时保持了高性能和安全性。
