/**
 * 权限检查中间件
 * 检查用户是否具有指定的权限
 * @param {String|Array} requiredPermissions - 需要的权限（字符串或数组）
 * @param {String} mode - 权限检查模式：'any'（任一权限）或 'all'（所有权限），默认'any'
 */
function hasPermission(requiredPermissions, mode = 'any') {
  return (req, res, next) => {
    try {
      // 确保用户已通过认证
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          data: null,
          message: '用户未认证'
        });
      }

      // 将单个权限转换为数组
      const permissions = Array.isArray(requiredPermissions) 
        ? requiredPermissions 
        : [requiredPermissions];

      const userPermissions = req.user.permissions || [];

      // 检查权限
      let hasRequiredPermission = false;
      
      if (mode === 'all') {
        // 需要拥有所有权限
        hasRequiredPermission = permissions.every(perm => userPermissions.includes(perm));
      } else {
        // 需要拥有任一权限（默认模式）
        hasRequiredPermission = permissions.some(perm => userPermissions.includes(perm));
      }

      if (!hasRequiredPermission) {
        return res.status(403).json({
          code: 403,
          data: null,
          message: '权限不足'
        });
      }

      next();
    } catch (error) {
      console.error('权限检查错误:', error.message);
      return res.status(500).json({
        code: 500,
        data: null,
        message: '权限检查失败'
      });
    }
  };
}

/**
 * 角色检查中间件
 * 检查用户是否具有指定的角色
 * @param {String|Array} requiredRoles - 需要的角色代码（字符串或数组）
 */
function hasRole(requiredRoles) {
  return (req, res, next) => {
    try {
      // 确保用户已通过认证
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          data: null,
          message: '用户未认证'
        });
      }

      // 将单个角色转换为数组
      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
      const userRoles = req.user.roles.map(role => role.code);

      // 检查是否拥有任一所需角色
      const hasRequiredRole = roles.some(role => userRoles.includes(role));

      if (!hasRequiredRole) {
        return res.status(403).json({
          code: 403,
          data: null,
          message: '角色权限不足'
        });
      }

      next();
    } catch (error) {
      console.error('角色检查错误:', error.message);
      return res.status(500).json({
        code: 500,
        data: null,
        message: '角色检查失败'
      });
    }
  };
}

/**
 * 超级管理员检查中间件
 * 检查用户是否为超级管理员
 */
function isSuperAdmin(req, res, next) {
  return hasRole('superAdmin')(req, res, next);
}

/**
 * 资源所有者检查中间件
 * 检查用户是否为资源的所有者（用于用户只能操作自己的资源）
 * @param {String} paramName - 路径参数名，默认为'id'
 */
function isOwner(paramName = 'id') {
  return (req, res, next) => {
    try {
      // 确保用户已通过认证
      if (!req.user) {
        return res.status(401).json({
          code: 401,
          data: null,
          message: '用户未认证'
        });
      }

      const resourceId = req.params[paramName];
      const userId = req.user.id;

      // 如果是超级管理员，直接通过
      const userRoles = req.user.roles.map(role => role.code);
      if (userRoles.includes('superAdmin')) {
        return next();
      }

      // 检查是否为资源所有者
      if (resourceId !== userId) {
        return res.status(403).json({
          code: 403,
          data: null,
          message: '只能操作自己的资源'
        });
      }

      next();
    } catch (error) {
      console.error('资源所有者检查错误:', error.message);
      return res.status(500).json({
        code: 500,
        data: null,
        message: '权限检查失败'
      });
    }
  };
}

module.exports = {
  hasPermission,
  hasRole,
  isSuperAdmin,
  isOwner
};
