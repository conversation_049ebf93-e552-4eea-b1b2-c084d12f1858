const { verifyToken, extractTokenFromHeader } = require('../utils/jwt');
const { query } = require('../config/database');
const redisUtils = require('../utils/redisUtils');

/**
 * JWT + Redis认证中间件
 * 验证JWT token并检查Redis中的token状态，获取用户信息
 */
async function authMiddleware(req, res, next) {
  try {
    // 从请求头中提取token
    const token = extractTokenFromHeader(req);

    if (!token) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: '未提供认证token'
      });
    }

    // 验证JWT token
    const decoded = verifyToken(token);

    // 检查Redis中是否存在对应的token（实际的过期控制）
    const userIdFromRedis = await redisUtils.validateToken(token);
    if (!userIdFromRedis) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token已失效，请重新登录'
      });
    }

    // 验证JWT中的用户ID与Redis中的用户ID是否一致
    if (decoded.userId !== userIdFromRedis) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token验证失败'
      });
    }

    const userId = userIdFromRedis;

    // 从数据库中获取用户信息（确保用户仍然存在且有效）
    const userSql = 'SELECT id, username, email, mp_open_id, create_time, update_time FROM user WHERE id = ?';
    const users = await query(userSql, [userId]);

    if (users.length === 0) {
      // 用户不存在，删除Redis中的token
      await redisUtils.deleteToken(token);
      return res.status(401).json({
        code: 401,
        data: null,
        message: '用户不存在或已被删除'
      });
    }

    const user = users[0];

    // 刷新Redis中token的过期时间（活跃用户延长会话）
    await redisUtils.refreshToken(token, 18000); // 重新设置5小时过期

    // 获取用户角色
    const rolesSql = `
      SELECT r.id, r.name, r.code
      FROM user_role ur
      JOIN role r ON ur.role_id = r.id
      WHERE ur.user_id = ? AND r.status = 1
    `;
    const roles = await query(rolesSql, [user.id]);

    // 获取用户的所有权限（通过角色权限关联表）
    const permissionsSql = `
      SELECT DISTINCT p.code
      FROM permissions p
      INNER JOIN role_permissions rp ON p.id = rp.permission_id
      INNER JOIN user_role ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = ? AND p.status = 1
      ORDER BY p.code
    `;
    const permissionResults = await query(permissionsSql, [user.id]);
    const permissions = permissionResults.map(p => p.code);

    // 将用户信息和权限添加到请求对象中
    req.user = {
      ...user,
      roles: roles.map(role => ({
        id: role.id,
        name: role.name,
        code: role.code
      })),
      permissions: permissions
    };

    next();
  } catch (error) {
    console.error('认证中间件错误:', error.message);
    
    if (error.message.includes('Token已过期')) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token已过期，请重新登录'
      });
    } else if (error.message.includes('Token无效')) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token无效'
      });
    } else {
      return res.status(500).json({
        code: 500,
        data: null,
        message: '认证验证失败'
      });
    }
  }
}

/**
 * 可选认证中间件
 * 如果有token则验证，没有token则跳过（用于一些可选登录的接口）
 */
async function optionalAuthMiddleware(req, res, next) {
  try {
    const token = extractTokenFromHeader(req);
    
    if (!token) {
      // 没有token，设置为匿名用户
      req.user = null;
      return next();
    }

    // 有token，执行正常的认证流程
    await authMiddleware(req, res, next);
  } catch (error) {
    // 认证失败，设置为匿名用户
    req.user = null;
    next();
  }
}

module.exports = {
  authMiddleware,
  optionalAuthMiddleware
};
