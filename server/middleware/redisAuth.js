const jwt = require('jsonwebtoken');
const redisUtils = require('../utils/redisUtils');
const User = require('../models/User');

/**
 * Redis Token验证中间件
 * 验证JWT token的同时检查Redis中是否存在对应的token记录
 */
const redisAuthMiddleware = async (req, res, next) => {
  try {
    // 获取token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: '未提供有效的认证token'
      });
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀

    // 验证JWT token
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    } catch (jwtError) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token无效或已过期'
      });
    }

    const userId = decoded.userId;

    // 检查Redis中是否存在对应的token
    const isValidInRedis = await redisUtils.validateUserToken(userId, token);
    if (!isValidInRedis) {
      return res.status(401).json({
        code: 401,
        data: null,
        message: 'Token已失效，请重新登录'
      });
    }

    // 获取用户信息
    const user = await User.findById(userId);
    if (!user) {
      // 如果用户不存在，删除Redis中的token
      await redisUtils.deleteUserToken(userId);
      return res.status(401).json({
        code: 401,
        data: null,
        message: '用户不存在'
      });
    }

    // 检查用户状态
    if (user.status !== 1) {
      // 如果用户被禁用，删除Redis中的token
      await redisUtils.deleteUserToken(userId);
      return res.status(401).json({
        code: 401,
        data: null,
        message: '用户账户已被禁用'
      });
    }

    // 刷新token过期时间（可选，延长活跃用户的session）
    await redisUtils.refreshUserToken(userId, 18000); // 重新设置5小时过期

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      mp_open_id: user.mp_open_id,
      create_time: user.create_time,
      update_time: user.update_time,
      status: user.status
    };

    next();
  } catch (error) {
    console.error('Redis认证中间件错误:', error.message);
    res.status(500).json({
      code: 500,
      data: null,
      message: '认证服务异常'
    });
  }
};

/**
 * 可选的Redis Token验证中间件
 * 如果token存在则验证，不存在则跳过（用于可选认证的接口）
 */
const optionalRedisAuthMiddleware = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    // 如果没有提供token，直接跳过
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    // 如果提供了token，则进行完整验证
    return redisAuthMiddleware(req, res, next);
  } catch (error) {
    console.error('可选Redis认证中间件错误:', error.message);
    next(); // 出错时跳过认证
  }
};

module.exports = {
  redisAuthMiddleware,
  optionalRedisAuthMiddleware
};
