-- 用户邮箱关联表
-- 用于管理用户与邮箱地址的关联关系

DROP TABLE IF EXISTS `user_emails`;
CREATE TABLE `user_emails` (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键ID',
  `user_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID，关联user表',
  `email_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '邮箱地址',
  `email_type` enum('primary', 'secondary', 'work', 'personal', 'other') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'secondary' COMMENT '邮箱类型：primary-主邮箱，secondary-次要邮箱，work-工作邮箱，personal-个人邮箱，other-其他',
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已验证：0-未验证，1-已验证',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否激活：0-禁用，1-启用',
  `verification_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '验证令牌',
  `verification_sent_at` datetime NULL DEFAULT NULL COMMENT '验证邮件发送时间',
  `verified_at` datetime NULL DEFAULT NULL COMMENT '验证完成时间',
  `last_email_received_at` datetime NULL DEFAULT NULL COMMENT '最后收到邮件时间',
  `email_count` int(11) NOT NULL DEFAULT 0 COMMENT '收到邮件总数',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱描述/备注',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_email`(`user_id`, `email_address`) USING BTREE COMMENT '用户邮箱唯一索引',
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_email_address`(`email_address`) USING BTREE,
  INDEX `idx_email_type`(`email_type`) USING BTREE,
  INDEX `idx_is_verified`(`is_verified`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_last_email_received`(`last_email_received_at`) USING BTREE,
  CONSTRAINT `fk_user_emails_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户邮箱关联表' ROW_FORMAT = Dynamic;

-- 创建触发器：确保每个用户只能有一个主邮箱
DELIMITER $$
CREATE TRIGGER `tr_user_emails_primary_unique` 
BEFORE INSERT ON `user_emails` 
FOR EACH ROW 
BEGIN
  IF NEW.email_type = 'primary' THEN
    -- 检查该用户是否已有主邮箱
    IF EXISTS (SELECT 1 FROM user_emails WHERE user_id = NEW.user_id AND email_type = 'primary' AND is_active = 1) THEN
      SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '每个用户只能有一个主邮箱';
    END IF;
  END IF;
END$$

CREATE TRIGGER `tr_user_emails_primary_unique_update` 
BEFORE UPDATE ON `user_emails` 
FOR EACH ROW 
BEGIN
  IF NEW.email_type = 'primary' AND OLD.email_type != 'primary' THEN
    -- 检查该用户是否已有其他主邮箱
    IF EXISTS (SELECT 1 FROM user_emails WHERE user_id = NEW.user_id AND email_type = 'primary' AND is_active = 1 AND id != NEW.id) THEN
      SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '每个用户只能有一个主邮箱';
    END IF;
  END IF;
END$$
DELIMITER ;

-- 插入示例数据（可选）
-- INSERT INTO `user_emails` (`id`, `user_id`, `email_address`, `email_type`, `is_verified`, `description`) VALUES
-- (UUID(), 'user-uuid-here', '<EMAIL>', 'primary', 1, '用户主邮箱'),
-- (UUID(), 'user-uuid-here', '<EMAIL>', 'work', 0, '工作邮箱');
