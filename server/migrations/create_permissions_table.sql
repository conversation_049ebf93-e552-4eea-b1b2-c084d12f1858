-- 创建权限表
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` varchar(36) NOT NULL,
  `code` varchar(100) NOT NULL COMMENT '权限代码，如：user:add',
  `name` varchar(100) NOT NULL COMMENT '权限名称，如：新增用户',
  `description` text COMMENT '权限描述',
  `group_code` varchar(50) NOT NULL COMMENT '权限分组代码，如：user',
  `group_name` varchar(50) NOT NULL COMMENT '权限分组名称，如：用户管理',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序顺序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_group_code` (`group_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 插入初始权限数据
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `group_code`, `group_name`, `sort_order`, `status`) VALUES
-- 用户管理权限
(UUID(), 'user:query', '查询用户', '查看用户列表和用户详情', 'user', '用户管理', 1, 1),
(UUID(), 'user:add', '新增用户', '创建新用户账户', 'user', '用户管理', 2, 1),
(UUID(), 'user:update', '编辑用户', '修改用户信息', 'user', '用户管理', 3, 1),
(UUID(), 'user:delete', '删除用户', '删除用户账户', 'user', '用户管理', 4, 1),
(UUID(), 'user:manage', '用户管理', '用户管理总权限', 'user', '用户管理', 5, 1),

-- 角色管理权限
(UUID(), 'role:query', '查询角色', '查看角色列表和角色详情', 'role', '角色管理', 1, 1),
(UUID(), 'role:add', '新增角色', '创建新角色', 'role', '角色管理', 2, 1),
(UUID(), 'role:update', '编辑角色', '修改角色信息和权限', 'role', '角色管理', 3, 1),
(UUID(), 'role:delete', '删除角色', '删除角色', 'role', '角色管理', 4, 1),
(UUID(), 'role:manage', '角色管理', '角色管理总权限', 'role', '角色管理', 5, 1),

-- 菜单管理权限
(UUID(), 'menu:query', '查询菜单', '查看菜单列表和菜单详情', 'menu', '菜单管理', 1, 1),
(UUID(), 'menu:add', '新增菜单', '创建新菜单', 'menu', '菜单管理', 2, 1),
(UUID(), 'menu:update', '编辑菜单', '修改菜单信息', 'menu', '菜单管理', 3, 1),
(UUID(), 'menu:delete', '删除菜单', '删除菜单', 'menu', '菜单管理', 4, 1),
(UUID(), 'menu:manage', '菜单管理', '菜单管理总权限', 'menu', '菜单管理', 5, 1),

-- 权限管理权限
(UUID(), 'permission:query', '查询权限', '查看权限列表和权限详情', 'permission', '权限管理', 1, 1),
(UUID(), 'permission:add', '新增权限', '创建新权限', 'permission', '权限管理', 2, 1),
(UUID(), 'permission:update', '编辑权限', '修改权限信息', 'permission', '权限管理', 3, 1),
(UUID(), 'permission:delete', '删除权限', '删除权限', 'permission', '权限管理', 4, 1),
(UUID(), 'permission:manage', '权限管理', '权限管理总权限', 'permission', '权限管理', 5, 1),

-- 个人资料权限
(UUID(), 'profile:query', '查看资料', '查看个人资料', 'profile', '个人资料', 1, 1),
(UUID(), 'profile:update', '编辑资料', '修改个人资料', 'profile', '个人资料', 2, 1),

-- 系统管理权限
(UUID(), 'system:config', '系统配置', '系统参数配置', 'system', '系统管理', 1, 1),
(UUID(), 'system:log', '系统日志', '查看系统日志', 'system', '系统管理', 2, 1),
(UUID(), 'system:backup', '数据备份', '数据备份和恢复', 'system', '系统管理', 3, 1);
