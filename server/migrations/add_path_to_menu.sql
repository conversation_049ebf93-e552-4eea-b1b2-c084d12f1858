-- 为menu表添加path字段
-- 执行时间: 2025-08-03

-- 添加path字段
ALTER TABLE menu ADD COLUMN path VARCHAR(255) DEFAULT NULL COMMENT '路由路径' AFTER title;

-- 根据现有数据更新path字段
UPDATE menu SET path = 'Index' WHERE name = 'Index';
UPDATE menu SET path = 'GetMailbox' WHERE name = 'GetMailbox';
UPDATE menu SET path = 'MyMailbox' WHERE name = 'MyMailbox';
UPDATE menu SET path = 'Contact' WHERE name = 'Contact';
UPDATE menu SET path = 'system/menu' WHERE name = 'MenuManagement';

-- 为Layout类型的菜单设置空路径（因为它们是容器）
UPDATE menu SET path = NULL WHERE component = 'Layout';

-- 显示更新结果
SELECT id, name, title, path, component FROM menu ORDER BY sort_order;
