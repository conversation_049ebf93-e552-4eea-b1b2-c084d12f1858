const express = require('express');
const Menu = require('../models/Menu');
const { authMiddleware } = require('../middleware/auth');
const { hasPermission } = require('../middleware/permission');
const router = express.Router();

// 获取当前用户的菜单
router.get('/user-menus', authMiddleware, async (req, res) => {
  try {
    const userId = req.user.id;
    const menus = await Menu.findByUserId(userId);

    res.json({
      code: 200,
      data: menus,
      message: '获取用户菜单成功'
    });
  } catch (error) {
    console.error('获取用户菜单失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取用户菜单失败'
    });
  }
});

// 获取所有菜单 - 需要菜单查询权限
router.get('/', authMiddleware, hasPermission('menu:query'), async (req, res) => {
  try {
    const menus = await Menu.findAll();

    res.json({
      code: 200,
      data: menus,
      message: '获取菜单列表成功'
    });
  } catch (error) {
    console.error('获取菜单列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取菜单列表失败'
    });
  }
});

// 根据ID获取菜单 - 需要菜单查询权限
router.get('/:id', authMiddleware, hasPermission('menu:query'), async (req, res) => {
  try {
    const { id } = req.params;
    const menu = await Menu.findById(id);

    if (!menu) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '菜单不存在'
      });
    }

    res.json({
      code: 200,
      data: menu,
      message: '获取菜单信息成功'
    });
  } catch (error) {
    console.error('获取菜单信息失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取菜单信息失败'
    });
  }
});

// 创建菜单 - 需要菜单添加权限
router.post('/', authMiddleware, hasPermission('menu:add'), async (req, res) => {
  try {
    const { parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark } = req.body;

    // 简单验证
    if (!name || !title || !path || !component) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '菜单名称、标题、路由路径和组件路径都是必填项'
      });
    }

    // 检查菜单名称是否已存在
    const existingMenuSql = 'SELECT id FROM menu WHERE name = ?';
    const { query } = require('../config/database');
    const existingMenus = await query(existingMenuSql, [name]);
    
    if (existingMenus.length > 0) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '菜单名称已存在'
      });
    }

    const menu = await Menu.create({
      parent_id,
      name,
      title,
      path,
      component,
      icon,
      sort_order,
      is_hidden,
      is_cache,
      status,
      remark
    });

    res.status(200).json({
      code: 200,
      data: menu,
      message: '菜单创建成功'
    });
  } catch (error) {
    console.error('创建菜单失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '创建菜单失败'
    });
  }
});

// 更新菜单 - 需要菜单更新权限
router.put('/:id', authMiddleware, hasPermission('menu:update'), async (req, res) => {
  try {
    const { id } = req.params;
    const { parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark } = req.body;

    if (!name || !title || !path || !component) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '菜单名称、标题、路由路径和组件路径都是必填项'
      });
    }

    // 检查菜单名称是否已被其他菜单使用
    const existingMenuSql = 'SELECT id FROM menu WHERE name = ? AND id != ?';
    const { query } = require('../config/database');
    const existingMenus = await query(existingMenuSql, [name, id]);
    
    if (existingMenus.length > 0) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '菜单名称已被其他菜单使用'
      });
    }

    const menu = await Menu.update(id, {
      parent_id,
      name,
      title,
      path,
      component,
      icon,
      sort_order,
      is_hidden,
      is_cache,
      status,
      remark
    });

    if (!menu) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '菜单不存在'
      });
    }

    res.json({
      code: 200,
      data: menu,
      message: '菜单更新成功'
    });
  } catch (error) {
    console.error('更新菜单失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '更新菜单失败'
    });
  }
});

// 删除菜单 - 需要菜单删除权限
router.delete('/:id', authMiddleware, hasPermission('menu:delete'), async (req, res) => {
  try {
    const { id } = req.params;
    const deleted = await Menu.delete(id);

    if (!deleted) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '菜单不存在'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '菜单删除成功'
    });
  } catch (error) {
    if (error.message === '存在子菜单，无法删除') {
      return res.status(400).json({
        code: 400,
        data: null,
        message: error.message
      });
    }

    console.error('删除菜单失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '删除菜单失败'
    });
  }
});

// 为角色分配菜单 - 需要角色管理权限
router.post('/assign-role', authMiddleware, hasPermission('role:update'), async (req, res) => {
  try {
    const { roleId, menuIds } = req.body;

    if (!roleId) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '角色ID是必填项'
      });
    }

    await Menu.assignToRole(roleId, menuIds || []);

    res.json({
      code: 200,
      data: null,
      message: '菜单分配成功'
    });
  } catch (error) {
    console.error('分配菜单失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '分配菜单失败'
    });
  }
});

// 获取角色的菜单 - 需要角色查询权限
router.get('/role/:roleId', authMiddleware, hasPermission('role:query'), async (req, res) => {
  try {
    const { roleId } = req.params;
    const menus = await Menu.findByRoleId(roleId);

    res.json({
      code: 200,
      data: menus,
      message: '获取角色菜单成功'
    });
  } catch (error) {
    console.error('获取角色菜单失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取角色菜单失败'
    });
  }
});

module.exports = router;
