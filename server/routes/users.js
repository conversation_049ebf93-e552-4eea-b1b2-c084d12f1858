const express = require('express');
const User = require('../models/User');
const { authMiddleware } = require('../middleware/auth');
const { hasPermission, isOwner } = require('../middleware/permission');
const router = express.Router();

// 获取所有用户 - 需要用户查询权限
router.get('/', authMiddleware, hasPermission('user:query'), async (req, res) => {
  try {
    const { page = 1, pageSize = 10, username, email, roleName } = req.query;

    // 构建筛选条件
    const filters = {};
    if (username) filters.username = username;
    if (email) filters.email = email;
    if (roleName) filters.roleName = roleName;

    const result = await User.findAll(parseInt(page), parseInt(pageSize), filters);

    res.json({
      code: 200,
      data: result.list,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      message: '获取用户列表成功'
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取用户列表失败'
    });
  }
});

// 根据ID获取用户 - 需要用户查询权限或者是本人
router.get('/:id', authMiddleware, hasPermission(['user:query', 'profile:query']), async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '用户不存在'
      });
    }

    res.json({
      code: 200,
      data: user,
      message: '获取用户信息成功'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取用户信息失败'
    });
  }
});

// 创建用户 - 需要用户添加权限
router.post('/', authMiddleware, hasPermission('user:add'), async (req, res) => {
  try {
    const { username, email, password, mp_open_id } = req.body;

    // 简单验证
    if (!username || !email || !password) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '用户名、邮箱和密码都是必填项'
      });
    }

    // 检查邮箱是否已存在
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '邮箱已被使用'
      });
    }

    const user = await User.create({ username, email, password, mp_open_id });

    res.status(200).json({
      code: 200,
      data: user,
      message: '用户创建成功'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      data: null,
      message: '创建用户失败'
    });
  }
});

// 更新用户 - 需要用户更新权限或者是本人更新自己
router.put('/:id', authMiddleware, hasPermission(['user:update', 'profile:update']), async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, mp_open_id } = req.body;

    if (!username || !email) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '用户名和邮箱都是必填项'
      });
    }

    const user = await User.update(id, { username, email, mp_open_id });

    if (!user) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '用户不存在'
      });
    }

    res.json({
      code: 200,
      data: user,
      message: '用户更新成功'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      data: null,
      message: '更新用户失败'
    });
  }
});

// 删除用户 - 需要用户删除权限
router.delete('/:id', authMiddleware, hasPermission('user:delete'), async (req, res) => {
  try {
    const { id } = req.params;
    const deleted = await User.delete(id);

    if (!deleted) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '用户不存在'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '用户删除成功'
    });
  } catch (error) {
    res.status(500).json({
      code: 500,
      data: null,
      message: '删除用户失败'
    });
  }
});

module.exports = router;
