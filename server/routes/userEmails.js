const express = require('express');
const UserEmail = require('../models/UserEmail');
const Email = require('../models/Email');
const { authMiddleware } = require('../middleware/auth');
const router = express.Router();

// 添加用户邮箱关联
router.post('/', authMiddleware, async (req, res) => {
  try {
    const { email_address } = req.body;
    const user_id = req.user.id;

    console.log('📝 添加用户邮箱关联请求:', {
      user_id,
      email_address,
      user: req.user.username
    });

    // 验证必填字段
    if (!email_address) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '邮箱地址是必填项'
      });
    }

    // 检查是否已经添加过该邮箱
    const existingEmail = await UserEmail.findByUserIdAndEmail(user_id, email_address);
    if (existingEmail) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '该邮箱已经添加到您的邮箱列表中'
      });
    }

    // 创建用户邮箱关联
    const userEmail = await UserEmail.create({
      user_id,
      email_address
    });

    res.json({
      code: 200,
      data: userEmail,
      message: '邮箱添加成功'
    });
  } catch (error) {
    console.error('添加用户邮箱关联失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: error.message || '添加邮箱失败'
    });
  }
});

// 获取用户关联邮箱的邮件列表 - 放在前面避免路由冲突
router.get('/emails', authMiddleware, async (req, res) => {
  try {
    const { page = 1, pageSize = 10, email_address } = req.query;
    const user_id = req.user.id;

    console.log('📊 获取用户邮箱邮件列表请求:', {
      user_id,
      page,
      pageSize,
      email_address,
      user: req.user.username
    });

    // 第一步：从user_emails表中获取用户关联的所有邮箱地址
    console.log('🔍 第一步：查找用户关联的邮箱地址...');
    const userEmailsResult = await UserEmail.findByUserId(user_id, 1, 1000); // 获取所有邮箱
    const userEmailAddresses = userEmailsResult.list.map(ue => ue.email_address);

    console.log('📧 用户关联的邮箱地址:', userEmailAddresses);

    if (userEmailAddresses.length === 0) {
      console.log('❌ 用户还没有添加任何邮箱');
      return res.json({
        code: 200,
        data: [],
        total: 0,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: 0,
        message: '您还没有添加任何邮箱，请先添加邮箱地址'
      });
    }

    // 第二步：根据邮箱地址去emails表中查找邮件
    console.log('🔍 第二步：根据邮箱地址查找邮件...');
    const targetAddresses = email_address ? [email_address] : userEmailAddresses;

    console.log('🎯 目标邮箱地址:', targetAddresses);

    // 构建查询选项
    const emailOptions = {
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      toAddresses: targetAddresses, // 使用用户关联的邮箱地址作为收件人地址
      sortBy: 'received_time',
      sortOrder: 'DESC'
    };

    console.log('📊 邮件查询选项:', emailOptions);

    // 获取邮件列表
    const emailsResult = await Email.findAll(emailOptions);

    console.log('📊 邮件查询结果:', {
      总数: emailsResult.total,
      当前页邮件数: emailsResult.list.length,
      页码: emailsResult.page,
      用户邮箱数: userEmailAddresses.length
    });

    res.json({
      code: 200,
      data: emailsResult.list,
      total: emailsResult.total,
      page: emailsResult.page,
      pageSize: emailsResult.pageSize,
      totalPages: emailsResult.totalPages,
      message: '获取邮件列表成功'
    });
  } catch (error) {
    console.error('获取用户邮箱邮件列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取邮件列表失败: ' + error.message
    });
  }
});

// 获取用户邮箱列表
router.get('/', authMiddleware, async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    const user_id = req.user.id;

    console.log('📊 获取用户邮箱列表请求:', {
      user_id,
      page,
      pageSize,
      user: req.user.username
    });

    const result = await UserEmail.findByUserId(user_id, parseInt(page), parseInt(pageSize));

    res.json({
      code: 200,
      data: result.list,
      total: result.total,
      page: result.page,
      pageSize: result.pageSize,
      totalPages: result.totalPages,
      message: '获取用户邮箱列表成功'
    });
  } catch (error) {
    console.error('获取用户邮箱列表失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取邮箱列表失败'
    });
  }
});

// 根据ID获取用户邮箱详情
router.get('/:id', authMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    const userEmail = await UserEmail.findById(id);

    if (!userEmail) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '邮箱关联不存在'
      });
    }

    // 检查是否属于当前用户
    if (userEmail.user_id !== user_id) {
      return res.status(403).json({
        code: 403,
        data: null,
        message: '无权访问该邮箱'
      });
    }

    res.json({
      code: 200,
      data: userEmail,
      message: '获取邮箱详情成功'
    });
  } catch (error) {
    console.error('获取用户邮箱详情失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '获取邮箱详情失败'
    });
  }
});

// 删除用户邮箱关联
router.delete('/:id', authMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    // 先查找邮箱关联，确保属于当前用户
    const userEmail = await UserEmail.findById(id);

    if (!userEmail) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '邮箱关联不存在'
      });
    }

    // 检查是否属于当前用户
    if (userEmail.user_id !== user_id) {
      return res.status(403).json({
        code: 403,
        data: null,
        message: '无权删除该邮箱'
      });
    }

    const success = await UserEmail.delete(id);

    if (!success) {
      return res.status(400).json({
        code: 400,
        data: null,
        message: '删除失败'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '邮箱删除成功'
    });
  } catch (error) {
    console.error('删除用户邮箱关联失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '删除邮箱失败'
    });
  }
});

// 根据邮箱地址删除用户邮箱关联
router.delete('/email/:emailAddress', authMiddleware, async (req, res) => {
  try {
    const { emailAddress } = req.params;
    const user_id = req.user.id;

    console.log('📝 删除用户邮箱关联请求:', {
      user_id,
      emailAddress,
      user: req.user.username
    });

    const success = await UserEmail.deleteByUserIdAndEmail(user_id, emailAddress);

    if (!success) {
      return res.status(404).json({
        code: 404,
        data: null,
        message: '邮箱关联不存在'
      });
    }

    res.json({
      code: 200,
      data: null,
      message: '邮箱删除成功'
    });
  } catch (error) {
    console.error('删除用户邮箱关联失败:', error);
    res.status(500).json({
      code: 500,
      data: null,
      message: '删除邮箱失败'
    });
  }
});

module.exports = router;
