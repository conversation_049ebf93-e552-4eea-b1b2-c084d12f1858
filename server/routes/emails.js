const express = require('express');
const router = express.Router();
const Email = require('../models/Email');

// 获取邮件列表
router.get('/', async (req, res) => {
  try {
    const options = {
      page: req.query.page,
      pageSize: req.query.pageSize,
      fromAddress: req.query.fromAddress,
      toAddress: req.query.toAddress,
      subject: req.query.subject,
      verificationCode: req.query.verificationCode,
      startTime: req.query.startTime,
      endTime: req.query.endTime,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await Email.findAll(options);

    res.json({
      code: 200,
      message: '获取邮件列表成功',
      data: result
    });
  } catch (error) {
    console.error('获取邮件列表失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 搜索邮件（支持全文搜索）
router.get('/search', async (req, res) => {
  try {
    const options = {
      keyword: req.query.keyword,
      page: req.query.page,
      pageSize: req.query.pageSize,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await Email.search(options);

    res.json({
      code: 200,
      message: '搜索邮件成功',
      data: result
    });
  } catch (error) {
    console.error('搜索邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 获取验证码邮件列表
router.get('/verification-codes', async (req, res) => {
  try {
    const options = {
      page: req.query.page,
      pageSize: req.query.pageSize,
      fromAddress: req.query.fromAddress,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await Email.findVerificationEmails(options);

    res.json({
      code: 200,
      message: '获取验证码邮件列表成功',
      data: result
    });
  } catch (error) {
    console.error('获取验证码邮件列表失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 获取邮件统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const stats = await Email.getStats();

    res.json({
      code: 200,
      message: '获取邮件统计成功',
      data: stats
    });
  } catch (error) {
    console.error('获取邮件统计失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 根据ID获取邮件详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '邮件ID不能为空',
        data: null
      });
    }

    const email = await Email.findById(id);

    if (!email) {
      return res.status(404).json({
        code: 404,
        message: '邮件不存在',
        data: null
      });
    }

    res.json({
      code: 200,
      message: '获取邮件详情成功',
      data: email
    });
  } catch (error) {
    console.error('获取邮件详情失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 创建邮件
router.post('/', async (req, res) => {
  try {
    // 检查message_id是否已存在
    if (req.body.message_id) {
      const existingEmail = await Email.findByMessageId(req.body.message_id);
      if (existingEmail) {
        return res.status(409).json({
          code: 409,
          message: '邮件已存在（message_id重复）',
          data: null
        });
      }
    }

    const email = await Email.create(req.body);

    res.status(201).json({
      code: 201,
      message: '创建邮件成功',
      data: email
    });
  } catch (error) {
    console.error('创建邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 更新邮件
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '邮件ID不能为空',
        data: null
      });
    }

    const email = await Email.update(id, req.body);

    if (!email) {
      return res.status(404).json({
        code: 404,
        message: '邮件不存在',
        data: null
      });
    }

    res.json({
      code: 200,
      message: '更新邮件成功',
      data: email
    });
  } catch (error) {
    console.error('更新邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 删除邮件
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id) {
      return res.status(400).json({
        code: 400,
        message: '邮件ID不能为空',
        data: null
      });
    }

    const deleted = await Email.delete(id);

    if (!deleted) {
      return res.status(404).json({
        code: 404,
        message: '邮件不存在',
        data: null
      });
    }

    res.json({
      code: 200,
      message: '删除邮件成功',
      data: null
    });
  } catch (error) {
    console.error('删除邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 批量删除邮件
router.delete('/', async (req, res) => {
  try {
    const { ids } = req.body;

    const result = await Email.deleteMany(ids);

    if (result.deletedCount === 0) {
      return res.status(404).json({
        code: 404,
        message: '没有找到要删除的邮件',
        data: null
      });
    }

    res.json({
      code: 200,
      message: `成功删除${result.deletedCount}封邮件`,
      data: result
    });
  } catch (error) {
    console.error('批量删除邮件失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

// 获取邮件统计信息
router.get('/stats/overview', async (req, res) => {
  try {
    const stats = await Email.getStats();

    res.json({
      code: 200,
      message: '获取邮件统计成功',
      data: stats
    });
  } catch (error) {
    console.error('获取邮件统计失败:', error);
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    });
  }
});

module.exports = router;
