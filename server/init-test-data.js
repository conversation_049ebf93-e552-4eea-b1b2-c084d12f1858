const { query } = require('./config/database');
const { hashPassword } = require('./utils/bcrypt');
const { v4: uuidv4 } = require('uuid');

/**
 * 初始化测试数据
 * 创建管理员用户、角色和菜单权限
 */
async function initTestData() {
  try {
    console.log('🚀 开始初始化测试数据...');

    // 1. 创建管理员角色
    console.log('📝 创建管理员角色...');
    const adminRoleId = uuidv4();
    const adminPermissions = [
      'user:add', 'user:delete', 'user:update', 'user:query', 'user:manage',
      'role:add', 'role:delete', 'role:update', 'role:query', 'role:manage',
      'menu:add', 'menu:delete', 'menu:update', 'menu:query', 'menu:manage'
    ];

    // 检查管理员角色是否已存在
    const existingAdminRole = await query('SELECT id FROM role WHERE code = ?', ['admin']);
    if (existingAdminRole.length === 0) {
      await query(
        'INSERT INTO role (id, name, code, description, permissions, status) VALUES (?, ?, ?, ?, ?, ?)',
        [adminRoleId, '管理员', 'admin', '系统管理员角色，拥有所有权限', JSON.stringify(adminPermissions), 1]
      );
      console.log('✅ 管理员角色创建成功');
    } else {
      console.log('ℹ️  管理员角色已存在，跳过创建');
    }

    // 2. 创建管理员用户
    console.log('👤 创建管理员用户...');
    const adminUserId = uuidv4();
    const hashedPassword = await hashPassword('admin123');

    // 检查管理员用户是否已存在（通过邮箱或用户名）
    const existingAdminUser = await query('SELECT id FROM user WHERE email = ? OR username = ?', ['<EMAIL>', 'admin']);
    if (existingAdminUser.length === 0) {
      await query(
        'INSERT INTO user (id, username, email, password) VALUES (?, ?, ?, ?)',
        [adminUserId, 'admin', '<EMAIL>', hashedPassword]
      );
      console.log('✅ 管理员用户创建成功');
    } else {
      console.log('ℹ️  管理员用户已存在，跳过创建');
      // 更新现有用户的密码为admin123
      await query('UPDATE user SET password = ? WHERE id = ?', [hashedPassword, existingAdminUser[0].id]);
      console.log('✅ 管理员密码已更新为admin123');
    }

    // 3. 为管理员分配角色
    console.log('🔗 为管理员分配角色...');
    const finalAdminUserId = existingAdminUser.length > 0 ? existingAdminUser[0].id : adminUserId;
    const finalAdminRoleId = existingAdminRole.length > 0 ? existingAdminRole[0].id : adminRoleId;

    // 检查用户角色关联是否已存在
    const existingUserRole = await query(
      'SELECT id FROM user_role WHERE user_id = ? AND role_id = ?',
      [finalAdminUserId, finalAdminRoleId]
    );

    if (existingUserRole.length === 0) {
      await query(
        'INSERT INTO user_role (id, user_id, role_id) VALUES (?, ?, ?)',
        [uuidv4(), finalAdminUserId, finalAdminRoleId]
      );
      console.log('✅ 管理员角色分配成功');
    } else {
      console.log('ℹ️  管理员角色关联已存在，跳过分配');
    }

    // 4. 创建系统菜单
    console.log('📋 创建系统菜单...');
    
    // 检查系统管理菜单是否已存在
    const existingSystemMenu = await query('SELECT id FROM menu WHERE name = ?', ['System']);
    let systemMenuId;
    
    if (existingSystemMenu.length === 0) {
      systemMenuId = uuidv4();
      await query(
        'INSERT INTO menu (id, parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [systemMenuId, null, 'System', '系统管理', null, 'Layout', 'setting', 100, 0, 1, 1, '系统管理模块']
      );
      console.log('✅ 系统管理菜单创建成功');
    } else {
      systemMenuId = existingSystemMenu[0].id;
      console.log('ℹ️  系统管理菜单已存在，跳过创建');
    }

    // 检查菜单管理菜单是否已存在
    const existingMenuManagement = await query('SELECT id FROM menu WHERE name = ?', ['MenuManagement']);
    let menuManagementId;
    
    if (existingMenuManagement.length === 0) {
      menuManagementId = uuidv4();
      await query(
        'INSERT INTO menu (id, parent_id, name, title, path, component, icon, sort_order, is_hidden, is_cache, status, remark) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [menuManagementId, systemMenuId, 'MenuManagement', '菜单管理', '/system/menu', 'system/menu/index', 'menu', 1, 0, 1, 1, '菜单管理功能']
      );
      console.log('✅ 菜单管理菜单创建成功');
    } else {
      menuManagementId = existingMenuManagement[0].id;
      console.log('ℹ️  菜单管理菜单已存在，跳过创建');
    }

    // 5. 为管理员角色分配菜单权限
    console.log('🔐 为管理员角色分配菜单权限...');
    
    // 获取所有菜单ID
    const allMenus = await query('SELECT id FROM menu WHERE status = 1');
    const menuIds = allMenus.map(menu => menu.id);

    // 删除现有的角色菜单关联
    await query('DELETE FROM role_menu WHERE role_id = ?', [finalAdminRoleId]);

    // 为管理员角色分配所有菜单权限
    for (const menuId of menuIds) {
      await query(
        'INSERT INTO role_menu (id, role_id, menu_id) VALUES (?, ?, ?)',
        [uuidv4(), finalAdminRoleId, menuId]
      );
    }
    console.log('✅ 菜单权限分配成功');

    console.log('\n🎉 测试数据初始化完成！');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📋 管理员账户信息:');
    console.log('   📧 邮箱: <EMAIL>');
    console.log('   🔑 密码: admin123');
    console.log('   👤 用户名: admin');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

  } catch (error) {
    console.error('❌ 初始化测试数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const { initDatabase } = require('./config/database');
  
  async function run() {
    try {
      await initDatabase();
      await initTestData();
      process.exit(0);
    } catch (error) {
      console.error('脚本执行失败:', error);
      process.exit(1);
    }
  }
  
  run();
}

module.exports = { initTestData };
