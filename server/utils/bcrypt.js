const bcrypt = require('bcryptjs');

// 盐值轮数，数值越高越安全但越慢
const SALT_ROUNDS = 12;

/**
 * 加密密码
 * @param {String} password - 明文密码
 * @returns {String} 加密后的密码hash
 */
async function hashPassword(password) {
  try {
    const salt = await bcrypt.genSalt(SALT_ROUNDS);
    const hash = await bcrypt.hash(password, salt);
    return hash;
  } catch (error) {
    console.error('密码加密失败:', error.message);
    throw new Error('密码加密失败');
  }
}

/**
 * 验证密码
 * @param {String} password - 明文密码
 * @param {String} hash - 存储的密码hash
 * @returns {Boolean} 密码是否匹配
 */
async function comparePassword(password, hash) {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('密码验证失败:', error.message);
    throw new Error('密码验证失败');
  }
}

/**
 * 生成随机密码
 * @param {Number} length - 密码长度，默认8位
 * @returns {String} 随机密码
 */
function generateRandomPassword(length = 8) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}

module.exports = {
  hashPassword,
  comparePassword,
  generateRandomPassword
};
