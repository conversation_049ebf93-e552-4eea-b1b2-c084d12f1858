# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/

# nyc测试覆盖率
.nyc_output

# Grunt中间和输出目录
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1声明文件
typings/

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 微束缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的REPL历史
.node_repl_history

# yarn完整性文件
.yarn-integrity

# parcel-bundler缓存
.cache
.parcel-cache

# next.js构建输出
.next

# nuxt.js构建输出
.nuxt

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless

# FuseBox缓存
.fusebox/

# DynamoDB本地文件
.dynamodb/
