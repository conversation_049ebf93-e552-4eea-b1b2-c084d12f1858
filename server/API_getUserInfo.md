# getUserInfo API 文档

## 概述

`getUserInfo` 接口是合并了原来的 `/profile` 和 `/me` 方法的新接口，提供完整的用户信息获取功能。

## 接口信息

- **URL**: `/api/auth/getUserInfo`
- **方法**: `GET`
- **认证**: 需要有效的JWT token
- **描述**: 获取当前用户的完整信息，包括用户基本信息、角色、权限和菜单

## 请求格式

### Headers
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

### 请求示例
```bash
curl -X GET \
  http://localhost:9090/api/auth/getUserInfo \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 响应格式

### 成功响应 (200)
```json
{
  "code": 200,
  "data": {
    "user": {
      "id": "user_123",
      "username": "admin",
      "email": "<EMAIL>",
      "mp_open_id": null,
      "create_time": "2025-08-02T10:00:00.000Z",
      "update_time": "2025-08-02T10:00:00.000Z",
      "status": 1
    },
    "roles": [
      {
        "id": "role_1",
        "name": "管理员",
        "code": "admin"
      },
      {
        "id": "role_2", 
        "name": "用户",
        "code": "user"
      }
    ],
    "permissions": [
      "user:read",
      "user:write",
      "user:delete",
      "role:read",
      "role:write"
    ],
    "menus": [
      {
        "id": "menu_1",
        "name": "用户管理",
        "path": "/users",
        "icon": "user",
        "sort": 1,
        "parent_id": null,
        "children": [
          {
            "id": "menu_2",
            "name": "用户列表",
            "path": "/users/list",
            "icon": "list",
            "sort": 1,
            "parent_id": "menu_1"
          }
        ]
      }
    ]
  },
  "message": "获取用户信息成功"
}
```

### 错误响应

#### 401 - 未提供token
```json
{
  "code": 401,
  "data": null,
  "message": "未提供认证token"
}
```

#### 401 - Token无效或过期
```json
{
  "code": 401,
  "data": null,
  "message": "Token已失效，请重新登录"
}
```

#### 401 - 用户不存在
```json
{
  "code": 401,
  "data": null,
  "message": "用户不存在或已被删除"
}
```

#### 401 - 用户被禁用
```json
{
  "code": 401,
  "data": null,
  "message": "用户账户已被禁用"
}
```

#### 500 - 服务器错误
```json
{
  "code": 500,
  "data": null,
  "message": "获取用户信息失败"
}
```

## 数据字段说明

### user 对象
| 字段 | 类型 | 描述 |
|------|------|------|
| id | string | 用户唯一标识 |
| username | string | 用户名 |
| email | string | 邮箱地址 |
| mp_open_id | string/null | 微信小程序OpenID |
| create_time | string | 创建时间 (ISO 8601) |
| update_time | string | 更新时间 (ISO 8601) |
| status | number | 用户状态 (1:正常, 0:禁用) |

### roles 数组
| 字段 | 类型 | 描述 |
|------|------|------|
| id | string | 角色唯一标识 |
| name | string | 角色名称 |
| code | string | 角色代码 |

### permissions 数组
- 类型: `string[]`
- 描述: 用户拥有的权限代码列表
- 示例: `["user:read", "user:write", "role:read"]`

### menus 数组
| 字段 | 类型 | 描述 |
|------|------|------|
| id | string | 菜单唯一标识 |
| name | string | 菜单名称 |
| path | string | 菜单路径 |
| icon | string | 菜单图标 |
| sort | number | 排序号 |
| parent_id | string/null | 父菜单ID |
| children | array | 子菜单列表 |

## 认证机制

### Token验证流程
1. 从请求头提取JWT token
2. 验证JWT签名和格式
3. 使用token从Redis获取用户ID
4. 验证JWT中的用户ID与Redis中的是否一致
5. 从数据库获取用户信息并检查状态
6. 刷新Redis中token的过期时间（活跃用户延长会话）

### 安全特性
- **双重验证**: JWT + Redis验证机制
- **实时撤销**: 可以通过删除Redis记录立即撤销token
- **会话延长**: 活跃用户自动延长会话时间
- **状态检查**: 实时检查用户是否被禁用

## 使用示例

### JavaScript (Axios)
```javascript
const token = localStorage.getItem('token');

try {
  const response = await axios.get('/api/auth/getUserInfo', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const { user, roles, permissions, menus } = response.data.data;
  
  // 使用用户信息
  console.log('用户:', user);
  console.log('角色:', roles);
  console.log('权限:', permissions);
  console.log('菜单:', menus);
  
} catch (error) {
  if (error.response?.status === 401) {
    // Token无效，跳转到登录页
    window.location.href = '/login';
  }
}
```

### JavaScript (Fetch)
```javascript
const token = localStorage.getItem('token');

fetch('/api/auth/getUserInfo', {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
.then(response => {
  if (response.status === 401) {
    // Token无效，跳转到登录页
    window.location.href = '/login';
    return;
  }
  return response.json();
})
.then(data => {
  if (data.code === 200) {
    const { user, roles, permissions, menus } = data.data;
    // 使用用户信息
  }
});
```

### Vue.js 组合式API
```javascript
import { ref, onMounted } from 'vue';
import axios from 'axios';

export default {
  setup() {
    const userInfo = ref(null);
    const loading = ref(false);
    
    const getUserInfo = async () => {
      loading.value = true;
      try {
        const response = await axios.get('/api/auth/getUserInfo');
        userInfo.value = response.data.data;
      } catch (error) {
        if (error.response?.status === 401) {
          // 处理认证失败
          router.push('/login');
        }
      } finally {
        loading.value = false;
      }
    };
    
    onMounted(() => {
      getUserInfo();
    });
    
    return {
      userInfo,
      loading,
      getUserInfo
    };
  }
};
```

## 与旧接口的对比

### 旧接口
- `/api/auth/profile` - 只返回基本用户信息和角色权限
- `/api/auth/me` - 返回用户信息、角色和菜单

### 新接口优势
- **统一接口**: 一个接口获取所有用户相关信息
- **完整数据**: 包含用户、角色、权限、菜单的完整信息
- **性能优化**: 减少客户端的多次请求
- **一致性**: 统一的数据结构和错误处理

## 迁移指南

### 从 /profile 迁移
```javascript
// 旧代码
const profileResponse = await axios.get('/api/auth/profile');
const user = profileResponse.data.data.user;

// 新代码
const userInfoResponse = await axios.get('/api/auth/getUserInfo');
const { user, roles, permissions, menus } = userInfoResponse.data.data;
```

### 从 /me 迁移
```javascript
// 旧代码
const meResponse = await axios.get('/api/auth/me');
const { user, roles, menus } = meResponse.data.data;

// 新代码 (现在还包含permissions)
const userInfoResponse = await axios.get('/api/auth/getUserInfo');
const { user, roles, permissions, menus } = userInfoResponse.data.data;
```

## 注意事项

1. **Token必需**: 此接口必须提供有效的JWT token
2. **实时验证**: 每次请求都会验证token的有效性
3. **会话延长**: 成功调用会自动延长token的过期时间
4. **权限检查**: 返回的权限和菜单基于用户的实际角色
5. **状态同步**: 用户状态变更会立即反映在接口响应中

## 错误处理建议

1. **401错误**: 立即跳转到登录页面
2. **500错误**: 显示友好的错误提示，可以重试
3. **网络错误**: 检查网络连接，提供重试选项
4. **超时**: 设置合理的请求超时时间
